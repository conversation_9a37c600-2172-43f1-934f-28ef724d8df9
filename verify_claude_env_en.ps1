# Verify Claude Code environment variables
Write-Host "=== Claude Code Environment Variables Verification ===" -ForegroundColor Green

# Get user-level environment variables
$baseUrl = [Environment]::GetEnvironmentVariable("ANTHROPIC_BASE_URL", "User")
$apiKey = [Environment]::GetEnvironmentVariable("ANTHROPIC_API_KEY", "User")
$authToken = [Environment]::GetEnvironmentVariable("ANTHROPIC_AUTH_TOKEN", "User")

Write-Host "`nUser-level environment variables:" -ForegroundColor Yellow
Write-Host "ANTHROPIC_BASE_URL: $baseUrl"
if ($apiKey) {
    Write-Host "ANTHROPIC_API_KEY: $($apiKey.Substring(0, 20))..." -ForegroundColor Cyan
} else {
    Write-Host "ANTHROPIC_API_KEY: Not set" -ForegroundColor Red
}
if ($authToken) {
    Write-Host "ANTHROPIC_AUTH_TOKEN: $($authToken.Substring(0, 20))..." -ForegroundColor Cyan
} else {
    Write-Host "ANTHROPIC_AUTH_TOKEN: Not set" -ForegroundColor Red
}

# Get current process environment variables
Write-Host "`nCurrent process environment variables:" -ForegroundColor Yellow
Write-Host "ANTHROPIC_BASE_URL: $env:ANTHROPIC_BASE_URL"
if ($env:ANTHROPIC_API_KEY) {
    Write-Host "ANTHROPIC_API_KEY: $($env:ANTHROPIC_API_KEY.Substring(0, 20))..." -ForegroundColor Cyan
} else {
    Write-Host "ANTHROPIC_API_KEY: Not set" -ForegroundColor Red
}
if ($env:ANTHROPIC_AUTH_TOKEN) {
    Write-Host "ANTHROPIC_AUTH_TOKEN: $($env:ANTHROPIC_AUTH_TOKEN.Substring(0, 20))..." -ForegroundColor Cyan
} else {
    Write-Host "ANTHROPIC_AUTH_TOKEN: Not set" -ForegroundColor Red
}

Write-Host "`n=== Verification Complete ===" -ForegroundColor Green
Write-Host "Note: If current process variables show as 'Not set', please restart your terminal or IDE." -ForegroundColor Red
