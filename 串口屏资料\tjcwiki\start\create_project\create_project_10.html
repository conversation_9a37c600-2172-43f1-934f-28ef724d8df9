<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>通过SD卡下载工程到串口屏 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="修改设备型号" href="create_project_11.html" />
    <link rel="prev" title="使用TFT文件下载助手(TFTFileDownload)通过串口下载工程到串口屏" href="create_project_9_2.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">快速入门</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../download_ide.html">下载和安装上位机软件</a></li>
<li class="toctree-l2"><a class="reference internal" href="../ide_introduce/index.html">上位机基本功能介绍</a></li>
<li class="toctree-l2"><a class="reference internal" href="../first_test.html">到手测试</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">创建工程</a><ul class="current">
<li class="toctree-l3 current"><a class="reference internal" href="index.html#id2">演示工程素材下载</a><ul class="current">
<li class="toctree-l4"><a class="reference internal" href="create_project_1.html">新建一个工程</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_2.html">制作开机页面</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_3.html">制作主页面1</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_4.html">报错：字库ID无效</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_5.html">创建字库和导入字库</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_6.html">按钮控件美化</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_7.html">通过代码修改控件属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_8.html">安装串口驱动</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_9.html">通过串口下载工程到串口屏</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_9_2.html">使用TFT文件下载助手(TFTFileDownload)通过串口下载工程到串口屏</a></li>
<li class="toctree-l4 current"><a class="current reference internal" href="#">通过SD卡下载工程到串口屏</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_11.html">修改设备型号</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_12.html">RTC相关</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_13.html">配置亮度</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_14.html">配置串口波特率</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_15.html">配置休眠</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_16.html">跨页面操作变量</a></li>
<li class="toctree-l4"><a class="reference internal" href="create_project_17.html">页面滑动切换</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../codeSpace.html">代码编写</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">快速入门</a> &raquo;</li>
          <li><a href="index.html">创建工程</a> &raquo;</li>
      <li>通过SD卡下载工程到串口屏</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="sd">
<h1>通过SD卡下载工程到串口屏<a class="headerlink" href="#sd" title="此标题的永久链接"></a></h1>
<p>通过microSD/TF卡下载工程到串口屏</p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>本方法建议用于批量生产时使用，只需向生产部门提供拷贝了TFT文件的TF卡即可，不需要向生产部门提供源代码（HMI文件）</p>
</div>
<section id="tft">
<h2>如何输出TFT生产文件<a class="headerlink" href="#tft" title="此标题的永久链接"></a></h2>
<p>点击文件-输出生产文件</p>
<img alt="../../_images/SDcardDownload3.png" src="../../_images/SDcardDownload3.png" />
<hr class="docutils" />
<p>点击输出,如果此时跳出“拒绝访问”，则建议退出电脑上的360杀毒软件（或其他杀毒软件），再重新输出即可</p>
<img alt="../../_images/SDcardDownload4.png" src="../../_images/SDcardDownload4.png" />
<p>输出的是与工程同名的.tft文件</p>
</section>
<hr class="docutils" />
<section id="id1">
<h2>如何使用读卡器<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p>将microSD卡插入读卡器，读卡器和microSD卡需要自行购买,microSD卡不能超过32GB（例如：512M、1GB、2GB、4GB、8GB、16GB、32GB都是可用的）</p>
<img alt="../../_images/SDcardDownload6.png" src="../../_images/SDcardDownload6.png" />
<p>将读卡器插入windows电脑的USB接口</p>
<p>打开“此电脑”</p>
<img alt="../../_images/SDcardDownload7.jpg" src="../../_images/SDcardDownload7.jpg" />
<p>可以在”设备和驱动器”发现刚刚插入的读卡器,可能你的不是叫做”U盘”,也可能叫做可移动硬盘或者其他</p>
<img alt="../../_images/SDcardDownload8.jpg" src="../../_images/SDcardDownload8.jpg" />
</section>
<section id="sdfat32">
<h2>格式化SD卡为fat32<a class="headerlink" href="#sdfat32" title="此标题的永久链接"></a></h2>
<p>右键选中盘符,点击格式化</p>
<img alt="../../_images/SDcardDownload9.jpg" src="../../_images/SDcardDownload9.jpg" />
<p>SD卡下载用于在批量生产时使用，使用前请确保SD卡没有隐藏分区（没有做过启动盘），SD卡容量不超过32G（例如：512M、1GB、2GB、4GB、8GB、16GB、32GB都是可用的），且格式化为fat32格式</p>
<img alt="../../_images/SDcardDownload2.png" src="../../_images/SDcardDownload2.png" />
</section>
<section id="id2">
<h2>使用SD卡下载工程到串口屏<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>确保SD卡不超过32GB，将tft文件拷贝到SD卡根目录，SD卡根目录有且仅只能有一个tft文件，否则会报错</p>
<img alt="../../_images/SDcardDownload5.png" src="../../_images/SDcardDownload5.png" />
<ul class="simple">
<li><p>将串口屏断电，SD卡插入串口屏的SD卡座中</p></li>
<li><p>串口屏重新上电，等待烧录完成</p></li>
<li><p>串口屏断电并取出SD卡</p></li>
<li><p>串口屏重新上电，程序下载完成</p></li>
</ul>
<hr class="docutils" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="create_project_9_2.html" class="btn btn-neutral float-left" title="使用TFT文件下载助手(TFTFileDownload)通过串口下载工程到串口屏" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="create_project_11.html" class="btn btn-neutral float-right" title="修改设备型号" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>