<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>赋值操作 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="运算操作" href="arithmetic_operation.html" />
    <link rel="prev" title="书写语法" href="index.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">书写语法</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">赋值操作</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id2">字符串属性赋值</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id3">数值属性赋值</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id4">字符串属性和数值属性相互转换</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id5">赋值操作-相关链接</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id6">赋值操作-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="arithmetic_operation.html">运算操作</a></li>
<li class="toctree-l2"><a class="reference internal" href="global_variable.html">跨页面赋值，全局变量操作</a></li>
<li class="toctree-l2"><a class="reference internal" href="hmi_logic.html">HMI逻辑语句</a></li>
<li class="toctree-l2"><a class="reference internal" href="name_array.html">数组/名称组使用说明</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">书写语法</a> &raquo;</li>
      <li>赋值操作</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>赋值操作<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<p>所有的赋值操作可以在上位编辑状态下写入控件事件中，也可以串口传输过来(串口传输记得加三个0xff的结束符)</p>
<div class="admonition warning">
<p class="admonition-title">警告</p>
<p>所有的赋值操作都不支持多余空格，添加进任何空格，编译都会报错</p>
<p>txt属性为字符串类型，字符串类型的属性赋值常量必须加双引号</p>
</div>
<section id="id2">
<h2>字符串属性赋值<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<div class="literal-block-wrapper docutils container" id="id8">
<div class="code-block-caption"><span class="caption-text">字符串赋值正确写法</span><a class="headerlink" href="#id8" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="o">//</span><span class="n">正确</span>
<span class="linenos">2</span><span class="n">t0</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;123&quot;</span>
<span class="linenos">3</span>
<span class="linenos">4</span><span class="o">//</span><span class="n">正确</span>
<span class="linenos">5</span><span class="n">t0</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="n">t1</span><span class="o">.</span><span class="n">txt</span>
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_1.jpg" src="../_images/assignment_operation_1.jpg" />
<div class="literal-block-wrapper docutils container" id="id9">
<div class="code-block-caption"><span class="caption-text">字符串赋值错误写法1</span><a class="headerlink" href="#id9" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//错误，字符串属性要用引号括起来
<span class="linenos">2</span>t0.txt=123
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_2.jpg" src="../_images/assignment_operation_2.jpg" />
<div class="literal-block-wrapper docutils container" id="id10">
<div class="code-block-caption"><span class="caption-text">字符串赋值错误写法2</span><a class="headerlink" href="#id10" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//错误，等号两边不要加空格
<span class="linenos">2</span>t0.txt = &quot;123&quot;
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_3.jpg" src="../_images/assignment_operation_3.jpg" />
<div class="literal-block-wrapper docutils container" id="id11">
<div class="code-block-caption"><span class="caption-text">字符串赋值错误写法3</span><a class="headerlink" href="#id11" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//错误，数值类型不能直接转换成文本
<span class="linenos">2</span>t0.txt=n0.val
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_4.jpg" src="../_images/assignment_operation_4.jpg" />
<div class="literal-block-wrapper docutils container" id="id12">
<div class="code-block-caption"><span class="caption-text">字符串赋值错误写法4</span><a class="headerlink" href="#id12" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//错误，语句末尾不要有分号
<span class="linenos">2</span>t0.txt=&quot;123&quot;;
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_5.jpg" src="../_images/assignment_operation_5.jpg" />
<div class="literal-block-wrapper docutils container" id="id13">
<div class="code-block-caption"><span class="caption-text">字符串赋值错误写法5</span><a class="headerlink" href="#id13" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//错误，多条赋值语句要分开写
<span class="linenos">2</span>t0.txt=&quot;123&quot; t1.txt=&quot;456&quot;
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_6.jpg" src="../_images/assignment_operation_6.jpg" />
</section>
<section id="id3">
<h2>数值属性赋值<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<div class="literal-block-wrapper docutils container" id="id14">
<div class="code-block-caption"><span class="caption-text">数值属性赋值</span><a class="headerlink" href="#id14" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>//给n0控件的val属性赋值123
<span class="linenos"> 2</span>n0.val=123
<span class="linenos"> 3</span>
<span class="linenos"> 4</span>//把h0控件的val属性赋值给n0控件的val属性
<span class="linenos"> 5</span>n0.val=h0.val
<span class="linenos"> 6</span>
<span class="linenos"> 7</span>//给系统变量dim赋值80（背光亮度立即变为80亮度）
<span class="linenos"> 8</span>dim=80
<span class="linenos"> 9</span>
<span class="linenos">10</span>//给系统变量bauds赋值115200(屏幕波特率立即变为115200)
<span class="linenos">11</span>bauds=115200
<span class="linenos">12</span>
<span class="linenos">13</span>//把屏幕当前的波特率系统变量赋值给n0控件的val属性
<span class="linenos">14</span>n0.val=bauds
<span class="linenos">15</span>
<span class="linenos">16</span>//设置p0控件显示id为2的图片
<span class="linenos">17</span>p0.pic=2
<span class="linenos">18</span>
<span class="linenos">19</span>//设置p0控件显示id为n0.val的图片
<span class="linenos">20</span>p0.pic=n0.val
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_7.jpg" src="../_images/assignment_operation_7.jpg" />
<div class="literal-block-wrapper docutils container" id="id15">
<div class="code-block-caption"><span class="caption-text">数值属性赋值错误写法1</span><a class="headerlink" href="#id15" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//错误：n0控件的val属性为数值类型，数值类型的属性赋值常量不应该有双引号
<span class="linenos">2</span>n0.val=&quot;123&quot;
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_8.jpg" src="../_images/assignment_operation_8.jpg" />
<div class="literal-block-wrapper docutils container" id="id16">
<div class="code-block-caption"><span class="caption-text">数值属性赋值错误写法2</span><a class="headerlink" href="#id16" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//错误：t0控件的txt属性是字符串类型，不能赋值给数值类型的属性
<span class="linenos">2</span>n0.val=t0.txt
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_9.jpg" src="../_images/assignment_operation_9.jpg" />
<div class="admonition tip">
<p class="admonition-title">小技巧</p>
<p>字符串类型和数值类型可以通过covx指令来实现相互转换赋值，具体请参考 <a class="reference internal" href="../commands/covx.html#covx"><span class="std std-ref">covx-变量类型转换</span></a></p>
</div>
</section>
<section id="id4">
<h2>字符串属性和数值属性相互转换<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<div class="literal-block-wrapper docutils container" id="id17">
<div class="code-block-caption"><span class="caption-text">val和txt属性相互转换1</span><a class="headerlink" href="#id17" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//错误，数值类型不能直接转换成文本
<span class="linenos">2</span>t0.txt=h0.val
<span class="linenos">3</span>
<span class="linenos">4</span>//正确
<span class="linenos">5</span>covx h0.val,t0.txt,0,0
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_10.jpg" src="../_images/assignment_operation_10.jpg" />
<div class="literal-block-wrapper docutils container" id="id18">
<div class="code-block-caption"><span class="caption-text">val和txt属性相互转换2</span><a class="headerlink" href="#id18" title="此代码块的永久链接"></a></div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//错误，文本类型不能直接转换成数值
<span class="linenos">2</span>n0.val=t0.txt
<span class="linenos">3</span>
<span class="linenos">4</span>//正确
<span class="linenos">5</span>covx t0.txt,n0.val,0,0
</pre></div>
</div>
</div>
<img alt="../_images/assignment_operation_11.jpg" src="../_images/assignment_operation_11.jpg" />
</section>
<section id="id5">
<h2>赋值操作-相关链接<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../QA/QA10.html#id1"><span class="std std-ref">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</span></a></p>
<p><a class="reference internal" href="../QA/QA11.html#txtval"><span class="std std-ref">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</span></a></p>
<p><a class="reference internal" href="global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
</section>
<section id="id6">
<h2>赋值操作-样例工程下载<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/书写语法/赋值操作.HMI">《赋值操作》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="书写语法" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="arithmetic_operation.html" class="btn btn-neutral float-right" title="运算操作" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>