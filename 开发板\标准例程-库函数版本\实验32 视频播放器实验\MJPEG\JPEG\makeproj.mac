--
-- makeproj.mac
--
-- This AppleScript builds Code Warrior PRO Release 2 project files for the
-- libjpeg library as well as the test programs 'cjpeg', 'djpeg', 'jpegtran'.
-- (We'd distribute real project files, except they're not text
-- and would create maintenance headaches.)
--
-- The script then compiles and links the library and the test programs.
-- NOTE: if you haven't already created a 'jconfig.h' file, the script
-- automatically copies 'jconfig.mac' to 'jconfig.h'.
--
-- To use this script, you must have AppleScript 1.1 or later installed
-- and a suitable AppleScript editor like Script Editor or Script Debugger
-- (http://www.latenightsw.com). Open this file with your AppleScript
-- editor and execute the "run" command to build the projects.
--
-- Thanks to <PERSON> and <PERSON> for this script.
-- Questions about this script can be <NAME_EMAIL>
--

on run

	choose folder with prompt ">>> Select IJG source folder <<<"
	set ijg_folder to result

	choose folder with prompt ">>> Select MetroWerks folder <<<"
	set cw_folder to result

	-- if jconfig.h doesn't already exist, copy jconfig.mac

	tell application "Finder"
		if not (exists file "jconfig.h" of ijg_folder) then
			duplicate {file "jconfig.mac" of folder ijg_folder}
			select file "jconfig.mac copy" of folder ijg_folder
			set name of selection to "jconfig.h"
		end if
	end tell

	tell application "CodeWarrior IDE 2.1"
	  with timeout of 10000 seconds

		-- create libjpeg project

		activate
		Create Project (ijg_folder as string) & "libjpeg.proj"
		Set Preferences of panel "Target Settings" to {Target Name:"libjpeg"}
		Set Preferences of panel "PPC Project" to {File Name:"libjpeg"}
		Set Preferences of panel "Target Settings" to {Linker:"MacOS PPC Linker"}
		Set Preferences of panel "PPC Project" to {Project Type:library}
		Set Preferences of panel "C/C++ Compiler" to {ANSI Strict:true}
		Set Preferences of panel "C/C++ Compiler" to {Enums Always Ints:true}
		Set Preferences of panel "PPC Codegen" to {Struct Alignment:PowerPC}
		Set Preferences of panel "PPC Linker" to {Generate SYM File:false}

		Add Files (ijg_folder as string) & "jaricom.c" To Segment 1
		Add Files (ijg_folder as string) & "jcapimin.c" To Segment 1
		Add Files (ijg_folder as string) & "jcapistd.c" To Segment 1
		Add Files (ijg_folder as string) & "jcarith.c" To Segment 1
		Add Files (ijg_folder as string) & "jctrans.c" To Segment 1
		Add Files (ijg_folder as string) & "jcparam.c" To Segment 1
		Add Files (ijg_folder as string) & "jdatadst.c" To Segment 1
		Add Files (ijg_folder as string) & "jcinit.c" To Segment 1
		Add Files (ijg_folder as string) & "jcmaster.c" To Segment 1
		Add Files (ijg_folder as string) & "jcmarker.c" To Segment 1
		Add Files (ijg_folder as string) & "jcmainct.c" To Segment 1
		Add Files (ijg_folder as string) & "jcprepct.c" To Segment 1
		Add Files (ijg_folder as string) & "jccoefct.c" To Segment 1
		Add Files (ijg_folder as string) & "jccolor.c" To Segment 1
		Add Files (ijg_folder as string) & "jcsample.c" To Segment 1
		Add Files (ijg_folder as string) & "jchuff.c" To Segment 1
		Add Files (ijg_folder as string) & "jcdctmgr.c" To Segment 1
		Add Files (ijg_folder as string) & "jfdctfst.c" To Segment 1
		Add Files (ijg_folder as string) & "jfdctflt.c" To Segment 1
		Add Files (ijg_folder as string) & "jfdctint.c" To Segment 1
		Add Files (ijg_folder as string) & "jdapimin.c" To Segment 1
		Add Files (ijg_folder as string) & "jdapistd.c" To Segment 1
		Add Files (ijg_folder as string) & "jdarith.c" To Segment 1
		Add Files (ijg_folder as string) & "jdtrans.c" To Segment 1
		Add Files (ijg_folder as string) & "jdatasrc.c" To Segment 1
		Add Files (ijg_folder as string) & "jdmaster.c" To Segment 1
		Add Files (ijg_folder as string) & "jdinput.c" To Segment 1
		Add Files (ijg_folder as string) & "jdmarker.c" To Segment 1
		Add Files (ijg_folder as string) & "jdhuff.c" To Segment 1
		Add Files (ijg_folder as string) & "jdmainct.c" To Segment 1
		Add Files (ijg_folder as string) & "jdcoefct.c" To Segment 1
		Add Files (ijg_folder as string) & "jdpostct.c" To Segment 1
		Add Files (ijg_folder as string) & "jddctmgr.c" To Segment 1
		Add Files (ijg_folder as string) & "jidctfst.c" To Segment 1
		Add Files (ijg_folder as string) & "jidctflt.c" To Segment 1
		Add Files (ijg_folder as string) & "jidctint.c" To Segment 1
		Add Files (ijg_folder as string) & "jdsample.c" To Segment 1
		Add Files (ijg_folder as string) & "jdcolor.c" To Segment 1
		Add Files (ijg_folder as string) & "jquant1.c" To Segment 1
		Add Files (ijg_folder as string) & "jquant2.c" To Segment 1
		Add Files (ijg_folder as string) & "jdmerge.c" To Segment 1
		Add Files (ijg_folder as string) & "jcomapi.c" To Segment 1
		Add Files (ijg_folder as string) & "jutils.c" To Segment 1
		Add Files (ijg_folder as string) & "jerror.c" To Segment 1
		Add Files (ijg_folder as string) & "jmemmgr.c" To Segment 1
		Add Files (ijg_folder as string) & "jmemmac.c" To Segment 1

		-- compile and link the library

		Make Project
		Close Project

		-- create cjpeg project

		activate
		Create Project (ijg_folder as string) & "cjpeg.proj"
		Set Preferences of panel "Target Settings" to {Target Name:"cjpeg"}
		Set Preferences of panel "PPC Project" to {File Name:"cjpeg"}
		Set Preferences of panel "Target Settings" to {Linker:"MacOS PPC Linker"}
		Set Preferences of panel "C/C++ Compiler" to {ANSI Strict:true}
		Set Preferences of panel "C/C++ Compiler" to {Enums Always Ints:true}
		Set Preferences of panel "PPC Codegen" to {Struct Alignment:PowerPC}
		Set Preferences of panel "PPC Linker" to {Generate SYM File:false}

		Add Files (ijg_folder as string) & "cjpeg.c" To Segment 1
		Add Files (ijg_folder as string) & "rdppm.c" To Segment 1
		Add Files (ijg_folder as string) & "rdgif.c" To Segment 1
		Add Files (ijg_folder as string) & "rdtarga.c" To Segment 1
		Add Files (ijg_folder as string) & "rdrle.c" To Segment 1
		Add Files (ijg_folder as string) & "rdbmp.c" To Segment 1
		Add Files (ijg_folder as string) & "rdswitch.c" To Segment 1
		Add Files (ijg_folder as string) & "cdjpeg.c" To Segment 1

		Add Files (ijg_folder as string) & "libjpeg" To Segment 2

		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:Metrowerks Standard Library:MSL C:Bin:MSL C.PPC.Lib" To Segment 3
		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:Metrowerks Standard Library:MSL C:Bin:MSL SIOUX.PPC.Lib" To Segment 3
		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:MacOS Support:Libraries:Runtime:Runtime PPC:MSL RuntimePPC.Lib" To Segment 3

		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:MacOS Support:Libraries:MacOS Common:InterfaceLib" To Segment 4
		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:MacOS Support:Libraries:MacOS Common:MathLib" To Segment 4

		-- compile and link cjpeg

		Make Project
		Close Project

		-- create djpeg project

		activate
		Create Project (ijg_folder as string) & "djpeg.proj"
		Set Preferences of panel "Target Settings" to {Target Name:"djpeg"}
		Set Preferences of panel "PPC Project" to {File Name:"djpeg"}
		Set Preferences of panel "Target Settings" to {Linker:"MacOS PPC Linker"}
		Set Preferences of panel "C/C++ Compiler" to {ANSI Strict:true}
		Set Preferences of panel "C/C++ Compiler" to {Enums Always Ints:true}
		Set Preferences of panel "PPC Codegen" to {Struct Alignment:PowerPC}
		Set Preferences of panel "PPC Linker" to {Generate SYM File:false}

		Add Files (ijg_folder as string) & "djpeg.c" To Segment 1
		Add Files (ijg_folder as string) & "wrppm.c" To Segment 1
		Add Files (ijg_folder as string) & "wrgif.c" To Segment 1
		Add Files (ijg_folder as string) & "wrtarga.c" To Segment 1
		Add Files (ijg_folder as string) & "wrrle.c" To Segment 1
		Add Files (ijg_folder as string) & "wrbmp.c" To Segment 1
		Add Files (ijg_folder as string) & "rdcolmap.c" To Segment 1
		Add Files (ijg_folder as string) & "cdjpeg.c" To Segment 1

		Add Files (ijg_folder as string) & "libjpeg" To Segment 2

		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:Metrowerks Standard Library:MSL C:Bin:MSL C.PPC.Lib" To Segment 3
		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:Metrowerks Standard Library:MSL C:Bin:MSL SIOUX.PPC.Lib" To Segment 3
		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:MacOS Support:Libraries:Runtime:Runtime PPC:MSL RuntimePPC.Lib" To Segment 3

		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:MacOS Support:Libraries:MacOS Common:InterfaceLib" To Segment 4
		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:MacOS Support:Libraries:MacOS Common:MathLib" To Segment 4

		-- compile and link djpeg

		Make Project
		Close Project

		-- create jpegtran project

		activate
		Create Project (ijg_folder as string) & "jpegtran.proj"
		Set Preferences of panel "Target Settings" to {Target Name:"jpegtran"}
		Set Preferences of panel "PPC Project" to {File Name:"jpegtran"}
		Set Preferences of panel "Target Settings" to {Linker:"MacOS PPC Linker"}
		Set Preferences of panel "C/C++ Compiler" to {ANSI Strict:true}
		Set Preferences of panel "C/C++ Compiler" to {Enums Always Ints:true}
		Set Preferences of panel "PPC Codegen" to {Struct Alignment:PowerPC}
		Set Preferences of panel "PPC Linker" to {Generate SYM File:false}

		Add Files (ijg_folder as string) & "jpegtran.c" To Segment 1
		Add Files (ijg_folder as string) & "rdswitch.c" To Segment 1
		Add Files (ijg_folder as string) & "cdjpeg.c" To Segment 1
		Add Files (ijg_folder as string) & "transupp.c" To Segment 1

		Add Files (ijg_folder as string) & "libjpeg" To Segment 2

		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:Metrowerks Standard Library:MSL C:Bin:MSL C.PPC.Lib" To Segment 3
		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:Metrowerks Standard Library:MSL C:Bin:MSL SIOUX.PPC.Lib" To Segment 3
		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:MacOS Support:Libraries:Runtime:Runtime PPC:MSL RuntimePPC.Lib" To Segment 3

		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:MacOS Support:Libraries:MacOS Common:InterfaceLib" To Segment 4
		Add Files (cw_folder as string) & "Metrowerks CodeWarrior:MacOS Support:Libraries:MacOS Common:MathLib" To Segment 4

		-- compile and link jpegtran

		Make Project
		Close Project

		quit

	  end timeout
	end tell
end run
