<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>认识控件 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="页面控件" href="Page.html" />
    <link rel="prev" title="所有控件详解" href="index.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">控件详解</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">认识控件</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id3">控件是什么</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id4">控件种类有多少</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id5">控件事件是什么</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id6">控件属性解析</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id7">控件属性读写</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id8">控件属性详解</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#type">type属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id">id属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="#objname">objname属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="#vscope">vscope属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="#txt">txt属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="#val">val属性</a></li>
<li class="toctree-l4"><a class="reference internal" href="#font">font属性</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#id9">控件属性-相关链接</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id10">控件属性-控件id对照表</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="Page.html">页面控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Text.html">文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Scrolling_text.html">滚动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Number.html">数字控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Xfloat.html">虚拟浮点数控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Button.html">按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Progress_bar.html">进度条控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Picture.html">图片控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Crop.html">切图控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Hotspot.html">触摸热区控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TouchCap.html">触摸捕捉控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gauge.html">指针控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Waveform.html">曲线波形控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Slider.html">滑块控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Timer.html">定时器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Variable.html">变量控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Dual-state_button.html">双态按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Checkbox.html">复选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Radio.html">单选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="QRcode.html">二维码控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Switch.html">状态开关控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ComboBox.html">下拉框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TextSelect.html">选择文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="SLText.html">滑动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="DataRecord.html">数据记录控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileBrowser.html">文件浏览器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileStream.html">文件流控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gmov.html">动画控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Video.html">视频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Audio.html">音频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ExPicture.html">外部图片控件</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">所有控件详解</a> &raquo;</li>
      <li>认识控件</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>认识控件<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>视频教程： <a class="reference external" href="https://www.bilibili.com/video/BV1vN4y1279w">认识控件</a></p>
</div>
<section id="id3">
<h2>控件是什么<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<p>控件是串口屏想实现相关功能的“功能组件”，需要串口屏显示什么内容就使用相对应的控件。</p>
<p>例如：</p>
<p>显示文本，用【文本控件】</p>
<p>显示图片，用【图片控件】</p>
<p>显示进度，用【进度条控件】</p>
<p>实现按键功能，用【按钮控件】</p>
<p>实现滑动功能，用【滑块控件】</p>
<p>要播放视频，用【视频控件】</p>
<p>••• •••</p>
<p>在串口屏上位机的界面编辑窗口中,一切你能看到的,都是控件</p>
<img alt="../_images/component_1.png" src="../_images/component_1.png" />
<hr class="docutils" />
<p>有些控件是看不见的，比如定时器，触摸捕捉等控件，位于底部的特殊控件栏</p>
<img alt="../_images/component_7.jpg" src="../_images/component_7.jpg" />
<hr class="docutils" />
</section>
<section id="id4">
<h2>控件种类有多少<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<p>《USART HMI》，目前已有“30种”控件，具体是哪些控件请打开软件查看控件“工具箱”查阅（控件工具箱位置如下图所示），同时我们根据市场需求会持续增加新控件。</p>
<img alt="../_images/component_6.jpg" src="../_images/component_6.jpg" />
<hr class="docutils" />
</section>
<section id="id5">
<h2>控件事件是什么<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<p>控件事件，指的是这个控件被操作时要执行的功能。</p>
<p>目前各种控件综合起来被操作的方式有以下几种类型：</p>
<p>1、触摸被按下：对应名称叫做【按下事件】</p>
<p>2、触摸被按下后弹起：对应名称叫做【弹起事件】</p>
<p>3、滑块控件被滑动：对应的名称叫做【滑动事件】</p>
<p>4、定时器定时运行：对应的名称叫做【定时事件】</p>
<p>5、动画播放完成：对应的名称叫做【播放完成事件】</p>
<p>6、视频播放完成：对应的名称叫做【播放完成事件】</p>
<hr class="docutils" />
</section>
<section id="id6">
<h2>控件属性解析<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h2>
<p>1、控件属性描述</p>
<p>控件属性是控件自己的一些设置项，上面提到想要什么功能就选择对应的控件。</p>
<p>比如想要显示文本，就用文本控件，但是选择文本控件后，显示什么内容？什么字体？字体什么颜色？文本背景什么颜色？字体间距多少等等这些信息怎么设置呢？</p>
<p>这就需要属性来定义了，这些信息都属于这个文本控件的属性，每个控件都有很多属性可以设置，用来定义他的显示风格。</p>
<p>通过对属性的简单编辑，便可将控件设置成您需要的效果；</p>
<p>选中任一控件,可以在右下角看到其属性栏</p>
<img alt="../_images/component_2.png" src="../_images/component_2.png" />
<hr class="docutils" />
<p>选中属性栏的任一属性，最底部会显示该属性的注释，例如 <code class="docutils literal notranslate"><span class="pre">txt</span></code>  属性，底部对应显示注释的为 <code class="docutils literal notranslate"><span class="pre">字符内容</span></code> ，即文本控件显示的字符内容</p>
<img alt="../_images/component_8.jpg" src="../_images/component_8.jpg" />
<hr class="docutils" />
<p>例如 <code class="docutils literal notranslate"><span class="pre">font</span></code>  属性，底部对应显示注释的为 <code class="docutils literal notranslate"><span class="pre">字库</span></code>  ，即文本控件调用的字库</p>
<img alt="../_images/component_9.jpg" src="../_images/component_9.jpg" />
<hr class="docutils" />
<p>控件属性有2种颜色,分别为绿色和黑色</p>
<p>绿色属性:控件编辑时可设置+运行中可改变，例如字库，字体色，背景色等</p>
<img alt="../_images/component_3.png" src="../_images/component_3.png" />
<p>黑色属性:控件编辑时可设置（运行中不可改变，例如objname：控件名称）或不可更改（例如type：控件类型）</p>
<img alt="../_images/component_4.png" src="../_images/component_4.png" />
<img alt="../_images/component_5.png" src="../_images/component_5.png" />
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>type属性不可设置,objname属性不可读取,id属性需要通过左上角置顶置底按钮才能进行更改</p>
</div>
</section>
<section id="id7">
<h2>控件属性读写<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h2>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> n0.val=100              //将n0赋值为100
<span class="linenos">2</span> t0.txt=&quot;淘晶驰电子&quot;      //文本控件显示淘晶驰电子
<span class="linenos">3</span> prints t0.txt,0         //将t0的文本内容从串口发送出去。
</pre></div>
</div>
<p>更多示例请参考 <a class="reference internal" href="../grammar/assignment_operation.html#id1"><span class="std std-ref">赋值操作</span></a></p>
<hr class="docutils" />
</section>
<section id="id8">
<h2>控件属性详解<a class="headerlink" href="#id8" title="此标题的永久链接"></a></h2>
<section id="type">
<h3>type属性<a class="headerlink" href="#type" title="此标题的永久链接"></a></h3>
<p>控件类型，可读，不可更改，不同类型的控件type属性不一样，相同类型的控件type属性一样</p>
<p>使用场景：</p>
<p>1、在键盘页面判断跳转过来的控件是什么类型的控件，不同的控件有不同的转换方法，详情请参考 <a class="reference internal" href="../advanced/keyboard/keyboard_principle.html#keyboardgetvalue"><span class="std std-ref">键盘如何获取到原始控件的值</span></a></p>
<p>2、在触摸捕捉中判断按下去的控件是什么类型</p>
</section>
<section id="id">
<h3>id属性<a class="headerlink" href="#id" title="此标题的永久链接"></a></h3>
<p>控件id，可通过上位机左上角的上下箭头置顶或置底，不能通过指令进行修改</p>
<p>每个页面中最底层都有一个页面控件，其id固定为0，可以设置当前页面的背景图片，背景色等操作</p>
<p>除了页面控件，每个页面最多放250个控件，占据id 1-250 ，每个页面各个控件之间id号是唯一的，不会重复</p>
<img alt="../_images/component_10.jpg" src="../_images/component_10.jpg" />
<p>可以理解为控件的图层，id大的控件会挡住id小的控件</p>
<p>使用场景：</p>
<p>通过名称组操作控件，请参考： <a class="reference internal" href="../grammar/name_array.html#id1"><span class="std std-ref">数组/名称组使用说明</span></a></p>
</section>
<section id="objname">
<h3>objname属性<a class="headerlink" href="#objname" title="此标题的永久链接"></a></h3>
<p>控件名称，可通过上位机进行修改，不可通过指令更改，不可读取</p>
<p>例如</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>t0.txt=n0.objname   //错误写法，objname属性不允许读取
n0.objname=&quot;n1&quot;   //错误写法，objname属性不允许写入
</pre></div>
</div>
</section>
<section id="vscope">
<h3>vscope属性<a class="headerlink" href="#vscope" title="此标题的永久链接"></a></h3>
<p>内存占用:0-私有;1-全局;可通过上位机进行修改，可读取，不可通过指令更改</p>
<p>私有占用只能在当前页面被访问，全局占用可以在所有页面被访问</p>
<p>当设置为私有时，跳转页面后，该控件占用的内存会被释放</p>
<p>重新返回该页面后该控件会恢复到最初的设置，可通过上位机进行修改，不可通过指令更改</p>
</section>
<section id="txt">
<h3>txt属性<a class="headerlink" href="#txt" title="此标题的永久链接"></a></h3>
<p>字符内容，通常出现在需要显示文本字符的控件中，例如文本控件，按钮控件，滚动文本控件等，修改此属性，就可以修改控件上显示的内容，可以理解为c语言中的字符串</p>
</section>
<section id="val">
<h3>val属性<a class="headerlink" href="#val" title="此标题的永久链接"></a></h3>
<p>数值，通常出现在需要显示数值的控件中，例如数字控件，虚拟浮点数控件等，修改此属性，就可以修改控件上显示的内容吗，数值主要用于加减乘除等计算，可以理解为c语言中的有符号整形（signed int）</p>
</section>
<section id="font">
<h3>font属性<a class="headerlink" href="#font" title="此标题的永久链接"></a></h3>
<p>控件所调用的字库id号，通常出现在需要显示文本字符和数值的控件中，例如文本控件，按钮控件，数字控件，数据记录控件等。导入不同的字库后修改此属性可以让控件显示不同的字体和字高</p>
</section>
</section>
<section id="id9">
<h2>控件属性-相关链接<a class="headerlink" href="#id9" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../QA/QA10.html#id1"><span class="std std-ref">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</span></a></p>
<p><a class="reference internal" href="../QA/QA11.html#txtval"><span class="std std-ref">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</span></a></p>
<p><a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
<p><a class="reference internal" href="../grammar/assignment_operation.html#id1"><span class="std std-ref">赋值操作</span></a></p>
</section>
<section id="id10">
<h2>控件属性-控件id对照表<a class="headerlink" href="#id10" title="此标题的永久链接"></a></h2>
<table class="longtable docutils align-default" id="id11">
<caption><span class="caption-text">控件id对照表</span><a class="headerlink" href="#id11" title="此表格的永久链接"></a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>控件类型</p></td>
<td><p>type值</p></td>
</tr>
<tr class="row-even"><td><p>文本</p></td>
<td><p>116</p></td>
</tr>
<tr class="row-odd"><td><p>滚动文本</p></td>
<td><p>55</p></td>
</tr>
<tr class="row-even"><td><p>数字</p></td>
<td><p>54</p></td>
</tr>
<tr class="row-odd"><td><p>虚拟浮点数</p></td>
<td><p>59</p></td>
</tr>
<tr class="row-even"><td><p>按钮</p></td>
<td><p>98</p></td>
</tr>
<tr class="row-odd"><td><p>进度条</p></td>
<td><p>106</p></td>
</tr>
<tr class="row-even"><td><p>图片</p></td>
<td><p>112</p></td>
</tr>
<tr class="row-odd"><td><p>切图</p></td>
<td><p>113</p></td>
</tr>
<tr class="row-even"><td><p>触摸热区</p></td>
<td><p>109</p></td>
</tr>
<tr class="row-odd"><td><p>触摸捕捉</p></td>
<td><p>5</p></td>
</tr>
<tr class="row-even"><td><p>指针</p></td>
<td><p>122</p></td>
</tr>
<tr class="row-odd"><td><p>曲线波形控件</p></td>
<td><p>0</p></td>
</tr>
<tr class="row-even"><td><p>滑块</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-odd"><td><p>定时器</p></td>
<td><p>51</p></td>
</tr>
<tr class="row-even"><td><p>变量</p></td>
<td><p>52</p></td>
</tr>
<tr class="row-odd"><td><p>双态按钮</p></td>
<td><p>53</p></td>
</tr>
<tr class="row-even"><td><p>复选框</p></td>
<td><p>56</p></td>
</tr>
<tr class="row-odd"><td><p>单选框</p></td>
<td><p>57</p></td>
</tr>
<tr class="row-even"><td><p>二维码</p></td>
<td><p>58</p></td>
</tr>
<tr class="row-odd"><td><p>状态开关</p></td>
<td><p>67</p></td>
</tr>
<tr class="row-even"><td><p>下拉框</p></td>
<td><p>61</p></td>
</tr>
<tr class="row-odd"><td><p>选择文本</p></td>
<td><p>68</p></td>
</tr>
<tr class="row-even"><td><p>滑动文本</p></td>
<td><p>62</p></td>
</tr>
<tr class="row-odd"><td><p>数据记录</p></td>
<td><p>66</p></td>
</tr>
<tr class="row-even"><td><p>文件浏览器</p></td>
<td><p>65</p></td>
</tr>
<tr class="row-odd"><td><p>文件流</p></td>
<td><p>63</p></td>
</tr>
<tr class="row-even"><td><p>动画</p></td>
<td><p>2</p></td>
</tr>
<tr class="row-odd"><td><p>视频</p></td>
<td><p>3</p></td>
</tr>
<tr class="row-even"><td><p>音频</p></td>
<td><p>4</p></td>
</tr>
<tr class="row-odd"><td><p>外部图片</p></td>
<td><p>60</p></td>
</tr>
<tr class="row-even"><td><p>页面</p></td>
<td><p>121</p></td>
</tr>
</tbody>
</table>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="所有控件详解" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="Page.html" class="btn btn-neutral float-right" title="页面控件" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>