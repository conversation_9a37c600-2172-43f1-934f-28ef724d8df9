<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>环形进度条样例工程 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="使用存储卡跨页面记录波形数据-文件流控件样例工程" href="tjcwiki_wavetest.html" />
    <link rel="prev" title="运行中发送图片到串口屏样例工程" href="tjcwiki_send_picture.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">资料下载</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../usart_hmi.html">上位机下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="../development_doc.html">开发文档下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tools_download.html">常用工具下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="../default_project.html">标准出厂工程样例</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">官方样例工程</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_more_language.html">多语言切换样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_dialog.html">串口屏弹窗样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_time_count.html">正计时和倒计时样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_time_count2.html">串口屏跨页面计时样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_RGB888toRGB565.html">RGB转换样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_wave.html">自定义曲线样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_51.html">51单片机样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_usart_assist.html">简易串口助手样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_conversion.html">进制转换样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_screensaver.html">自定义屏幕保护样例工程/低功耗/睡眠休眠替代</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_txt2float.html">虚拟浮点数与文本控件互相转换样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_little2big.html">大小端数据互相转换样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_setTime.html">显示时间和设置时间样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_heartbeat.html">串口屏实现通讯心跳样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_send_picture.html">运行中发送图片到串口屏样例工程</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">环形进度条样例工程</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id2">环形进度条-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_wavetest.html">使用存储卡跨页面记录波形数据-文件流控件样例工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_tft_download.html">X系列给其他屏幕下载工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="tjcwiki_resource_collection.html">淘晶驰资料合集</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../ui_demo.html">UI样例工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../other_project.html">网友提供应用样例</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tjc_game.html">游戏工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../font_download.html">免费字体下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="../voice_download.html">声音资源下载</a></li>
<li class="toctree-l2"><a class="reference internal" href="../scheme_download.html">原理图下载</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">资料下载</a> &raquo;</li>
          <li><a href="index.html">官方样例工程</a> &raquo;</li>
      <li>环形进度条样例工程</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>环形进度条样例工程<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<img alt="../../_images/tjcwiki_round_progressbar1.jpg" src="../../_images/tjcwiki_round_progressbar1.jpg" />
<p>使用图片实现的，所有系列都可以实现</p>
<p>资料中提供了 50*50 100*100 150*150 200*200 250*250 300*300 350*350 400*400 450*450 500*500 550*550 600*600 分辨率的素材</p>
<p>如果客户需要生成其他颜色或者分辨率的素材，也可以自行通过代码来生成环形素材，python代码已经提供在压缩包中</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>如果需要使用python来自行生成素材，请右键“素材”文件夹，以文件夹的型式打开，否则无法在对应的目录下生成图片素材</p>
</div>
<img alt="../../_images/tjcwiki_round_progressbar2.jpg" src="../../_images/tjcwiki_round_progressbar2.jpg" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>仅建议更改以下这4个参数：</p>
</div>
<p>图片的边长（size_x），图片必须是正方形的</p>
<p>进度条步进值（progress_step）</p>
<p>生成图片的背景颜色（background_color）</p>
<p>生成图片的进度条上的颜色（progress_color）</p>
<p>生成图片的进度条上未到达部分的颜色（empty_color）</p>
<img alt="../../_images/tjcwiki_round_progressbar3.jpg" src="../../_images/tjcwiki_round_progressbar3.jpg" />
<p>最后用数字控件或者图片控件来显示对应的图片即可</p>
<p>如果需要让数字控件中的数值颜色和进度条的颜色一致的话，选中数字控件的pco属性，然后选择select</p>
<img alt="../../_images/tjcwiki_round_progressbar4.jpg" src="../../_images/tjcwiki_round_progressbar4.jpg" />
<p>将对应的RGB数值填入即可</p>
<img alt="../../_images/tjcwiki_round_progressbar5.jpg" src="../../_images/tjcwiki_round_progressbar5.jpg" />
<p>这是使用python代码实现的自动</p>
<section id="id2">
<h2>环形进度条-样例工程下载<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/资料下载/官方样例工程/环形进度条/环形进度条.zip">《环形进度条》演示工程下载</a></p>
<hr class="docutils" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="tjcwiki_send_picture.html" class="btn btn-neutral float-left" title="运行中发送图片到串口屏样例工程" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="tjcwiki_wavetest.html" class="btn btn-neutral float-right" title="使用存储卡跨页面记录波形数据-文件流控件样例工程" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>