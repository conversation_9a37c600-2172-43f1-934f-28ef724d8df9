<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>串口屏发送数据给单片机 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="串口助手软件(sscom)和屏幕联调" href="../debug_with_sscom.html" />
    <link rel="prev" title="单片机发送数据给串口屏" href="mcu2tjc.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../start/index.html">快速入门</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">串口屏调试</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../simulator/index.html">模拟器调试基础知识</a></li>
<li class="toctree-l2"><a class="reference internal" href="../connect_ttl.html">和USB转TTL的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="../connect_mcu.html">和单片机的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="../base/index.html">联机调试基础知识</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">串口屏通讯协议</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="mcu2tjc.html">单片机发送数据给串口屏</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">串口屏发送数据给单片机</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id2">定义发送的数据格式</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3">下发数值格式的数据</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4">下发字符串格式的数据</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">《串口屏发送数据给单片机例程》下载</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7">单片机如何解析串口屏发出的数据</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../debug_with_sscom.html">串口助手软件(sscom)和屏幕联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../debug_with_vspd.html">串口助手软件和串口屏模拟器联调1</a></li>
<li class="toctree-l2"><a class="reference internal" href="../debug_with_vspd2.html">串口助手软件和串口屏模拟器联调2</a></li>
<li class="toctree-l2"><a class="reference internal" href="../stm32/index.html">与stm32单片机联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../timcu/index.html">与TI（德州仪器）单片机联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../arduino/index.html">与arduino联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../micropython/index.html">与MicroPython联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../python/index.html">与python联调</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">串口屏调试</a> &raquo;</li>
          <li><a href="index.html">串口屏通讯协议</a> &raquo;</li>
      <li>串口屏发送数据给单片机</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>串口屏发送数据给单片机<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<section id="id2">
<h2>定义发送的数据格式<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>淘晶驰没有定义固定的串口屏发送给单片机的数据格式，您需要自己定义格式</p>
<p>其原理相当于在单片机上实现串口屏上的主动解析</p>
<p>我们需要先定义一个符合我们需求的通讯协议</p>
<p>常见的通讯协议的格式如下</p>
<p>帧头 + 帧长度 + 帧内容 + 帧校验 + 帧尾</p>
<p>上面的格式中有些事可以被省略掉的，例如淘晶驰通讯协议是 帧内容 + 帧尾（3个0xff）</p>
<p>具体需要哪些格式，不需要哪些格式，取决于项目的具体需求</p>
<p>假设使用串口屏控制4个led灯，串口屏需要下发的参数为灯的状态（开或关）</p>
<p>通讯协议举例（帧头 + 帧内容 + 帧尾）：</p>
<p>当我们做汇报时，要先喊一声“报告!”，提醒其他人我们要开始说话了，这个相当于帧头</p>
<p>接下来我们开始说话，这个相当于帧内容</p>
<p>说完后，我们说一句“完毕!”，提醒大家我们说完了，这个相当于帧尾</p>
<p>这就是我们定义的一个通讯协议的结构，即“帧头 + 帧内容 + 帧尾”</p>
</section>
<section id="id3">
<h2>下发数值格式的数据<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<p>我们以此结构来传输led数据</p>
<p>备注:4个led用双态按钮来实现,led关闭时,对应的val属性是0，led开启时,对应的val属性是1。</p>
<p>帧头为 55</p>
<p>帧尾为 ff ff ff</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>//发出的数据如下
55 01 00 ff ff ff  //含义：1号led关闭
55 04 01 ff ff ff  //含义：4号led打开
55 01 01 ff ff ff  //含义：1号led打开
55 04 00 ff ff ff  //含义：4号led关闭
</pre></div>
</div>
<p>代码示例：</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">//</span><span class="n">发送1号灯状态</span>
<span class="n">printh</span> <span class="mi">55</span> <span class="mi">01</span>         <span class="o">//</span><span class="n">发送帧头和灯的编号</span>
<span class="n">prints</span> <span class="n">led1</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led1的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">printh</span> <span class="n">ff</span> <span class="n">ff</span> <span class="n">ff</span>      <span class="o">//</span><span class="n">发送帧尾</span>

<span class="o">//</span><span class="n">发送2号灯状态</span>
<span class="n">printh</span> <span class="mi">55</span> <span class="mi">02</span>         <span class="o">//</span><span class="n">发送帧头和灯的编号</span>
<span class="n">prints</span> <span class="n">led2</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led2的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">printh</span> <span class="n">ff</span> <span class="n">ff</span> <span class="n">ff</span>      <span class="o">//</span><span class="n">发送帧尾</span>

<span class="o">//</span><span class="n">发送3号灯状态</span>
<span class="n">printh</span> <span class="mi">55</span> <span class="mi">03</span>         <span class="o">//</span><span class="n">发送帧头和灯的编号</span>
<span class="n">prints</span> <span class="n">led3</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led3的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">printh</span> <span class="n">ff</span> <span class="n">ff</span> <span class="n">ff</span>      <span class="o">//</span><span class="n">发送帧尾</span>

<span class="o">//</span><span class="n">发送4号灯状态</span>
<span class="n">printh</span> <span class="mi">55</span> <span class="mi">04</span>         <span class="o">//</span><span class="n">发送帧头和灯的编号</span>
<span class="n">prints</span> <span class="n">led4</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led4的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">printh</span> <span class="n">ff</span> <span class="n">ff</span> <span class="n">ff</span>      <span class="o">//</span><span class="n">发送帧尾</span>
</pre></div>
</div>
<p>也可以一次性下发4个灯的状态</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>55 01 01 01 01 ff ff ff  //含义：4个灯状态分别为：开、开、开、开
55 00 00 00 00 ff ff ff  //含义：4个灯状态分别为：关、关、关、关
55 00 01 00 01 ff ff ff  //含义：4个灯状态分别为：关、开、关、开
55 01 00 01 00 ff ff ff  //含义：4个灯状态分别为：开、关、开、关
</pre></div>
</div>
<p>代码示例：</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">//</span><span class="n">一次性下发4个灯的状态</span>
<span class="n">printh</span> <span class="mi">55</span>            <span class="o">//</span><span class="n">发送帧头</span>
<span class="n">prints</span> <span class="n">led1</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led1的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led2</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led2的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led3</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led3的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led4</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led4的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">printh</span> <span class="n">ff</span> <span class="n">ff</span> <span class="n">ff</span>      <span class="o">//</span><span class="n">发送帧尾</span>
</pre></div>
</div>
<p>这里的帧头或者帧尾你可以改为其他你想要的，例如帧头改为0x33，帧尾用0d 0a（即回车换行）取代3个0xff</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>33 01 01 01 01 0d 0a  //含义：4个灯状态分别为：开、开、开、开
</pre></div>
</div>
<p>代码示例：</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">//</span><span class="n">一次性下发4个灯的状态</span>
<span class="n">printh</span> <span class="mi">33</span>            <span class="o">//</span><span class="n">发送帧头</span>
<span class="n">prints</span> <span class="n">led1</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led1的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led2</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led2的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led3</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led3的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led4</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led4的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">printh</span> <span class="mi">0</span><span class="n">d</span> <span class="mi">0</span><span class="n">a</span>         <span class="o">//</span><span class="n">发送帧尾</span>
</pre></div>
</div>
<p>也可以去掉帧头或者帧尾</p>
<p>1、去掉帧头</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>01 01 01 01 0d 0a  //含义：4个灯状态分别为：开、开、开、开
</pre></div>
</div>
<p>代码示例：</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">//</span><span class="n">一次性下发4个灯的状态</span>
<span class="n">prints</span> <span class="n">led1</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led1的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led2</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led2的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led3</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led3的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led4</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led4的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">printh</span> <span class="mi">0</span><span class="n">d</span> <span class="mi">0</span><span class="n">a</span>         <span class="o">//</span><span class="n">发送帧尾</span>
</pre></div>
</div>
<p>2、去掉帧尾</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>33 01 01 01 01  //含义：4个灯状态分别为：开、开、开、开
</pre></div>
</div>
<p>代码示例：</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">//</span><span class="n">一次性下发4个灯的状态</span>
<span class="n">printh</span> <span class="mi">33</span>            <span class="o">//</span><span class="n">发送帧头</span>
<span class="n">prints</span> <span class="n">led1</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led1的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led2</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led2的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led3</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led3的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led4</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led4的数据</span><span class="p">,</span><span class="n">长度1字节</span>
</pre></div>
</div>
<p>3、因为每次都是固定下发4个数据，因此连帧头帧尾都不要其实问题也不大,但是存在一定的解析错误的风险</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>01 01 01 01  //含义：4个灯状态分别为：开、开、开、开
</pre></div>
</div>
<p>代码示例：</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">//</span><span class="n">一次性下发4个灯的状态</span>
<span class="n">prints</span> <span class="n">led1</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led1的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led2</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led2的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led3</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led3的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led4</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led4的数据</span><span class="p">,</span><span class="n">长度1字节</span>
</pre></div>
</div>
<p>4、如果需要校验，可以使用modbus_crc16</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>33 01 01 01 01 B0 68   //含义：4个灯状态分别为：开、开、开、开，modbus_crc16校验值为 B0 68
33 00 00 00 00 20 04   //含义：4个灯状态分别为：关、关、关、关，modbus_crc16校验值为 20 04
33 00 01 00 01 B0 04   //含义：4个灯状态分别为：关、开、关、开，modbus_crc16校验值为 B0 04
33 01 00 01 00 20 68   //含义：4个灯状态分别为：开、关、开、关，modbus_crc16校验值为 20 68
</pre></div>
</div>
<p>代码示例：</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">crcrest</span> <span class="mi">1</span><span class="p">,</span><span class="mh">0xffff</span>     <span class="o">//</span><span class="n">复位crc</span>
<span class="n">printh</span> <span class="mi">33</span>            <span class="o">//</span><span class="n">发送帧头</span>
<span class="n">crcputh</span> <span class="mi">33</span>           <span class="o">//</span><span class="n">放入帧头</span>
<span class="n">prints</span> <span class="n">led1</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led1的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">crcputs</span> <span class="n">led1</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>   <span class="o">//</span><span class="n">放入led1的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led2</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led2的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">crcputs</span> <span class="n">led2</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>   <span class="o">//</span><span class="n">放入led2的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led3</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led3的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">crcputs</span> <span class="n">led3</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>   <span class="o">//</span><span class="n">放入led3的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">led4</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>    <span class="o">//</span><span class="n">发送led4的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">crcputs</span> <span class="n">led4</span><span class="o">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span>   <span class="o">//</span><span class="n">放入led4的数据</span><span class="p">,</span><span class="n">长度1字节</span>
<span class="n">prints</span> <span class="n">crcval</span><span class="p">,</span><span class="mi">2</span>      <span class="o">//</span><span class="n">发送crc校验结果</span><span class="p">,</span><span class="n">长度2字节</span>
</pre></div>
</div>
<p>使用crc校验的步骤:</p>
<p>1、复位crc校验</p>
<p>2、放入要校验的数据，如果是printh就用crcputh，如果是prints就用crcputs</p>
<p>3、发送校验结果crcval，长度一定是2字节</p>
</section>
<section id="id4">
<h2>下发字符串格式的数据<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<p>代码示例：</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>//发送用户名 55:帧头，01:代表下发的是用户名
printh 55 01
prints username.txt,0      //发送用户名字符串
printh 0d 0a               //发送帧尾
//发送密码 55:帧头，02:代表下发的是密码
printh 55 02
prints password.txt,0      //发送password字符串
printh 0d 0a               //发送帧尾
</pre></div>
</div>
<p>对于字符串这种长度不固定的数据，建议发送字符串长度方便单片机进行解析</p>
<p>代码示例：</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>//发送用户名 55:帧头，01:代表下发的是用户名
printh 55 01
btlen username.txt,sys0    //检测字符串长度并放入sys0
prints sys0,1              //下发1字节的字符串长度,可以表示0-255个字节,在大部分场景下都是适用的
prints username.txt,0      //发送username字符串
printh 0d 0a               //发送帧尾
//发送密码 55:帧头，02:代表下发的是密码
printh 55 02
btlen password.txt,sys0    //检测字符串长度并放入sys0
prints sys0,1              //下发1字节的字符串长度,可以表示0-255个字节,在大部分场景下都是适用的
prints password.txt,0      //发送password字符串
printh 0d 0a               //发送帧尾
</pre></div>
</div>
<img alt="../../_images/tjc2mcu_1.jpg" src="../../_images/tjc2mcu_1.jpg" />
</section>
<section id="id5">
<h2>《串口屏发送数据给单片机例程》下载<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏调试/串口屏发送数据给单片机/led状态下发.HMI">《led状态下发》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏调试/串口屏发送数据给单片机/串口下发用户名和密码.HMI">《串口下发用户名和密码》演示工程下载</a></p>
</section>
<section id="id7">
<h2>单片机如何解析串口屏发出的数据<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h2>
<p>请参考 <a class="reference internal" href="../stm32/hal_lib/stm32cubeide.html#stm32cubeide-hal"><span class="std std-ref">使用stm32cubeide(HAL库)开发</span></a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="mcu2tjc.html" class="btn btn-neutral float-left" title="单片机发送数据给串口屏" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="../debug_with_sscom.html" class="btn btn-neutral float-right" title="串口助手软件(sscom)和屏幕联调" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>