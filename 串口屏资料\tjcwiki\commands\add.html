<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>add-往曲线控件添加数据 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="addt-曲线数据透传指令" href="addt.html" />
    <link rel="prev" title="touch_j-触摸校准" href="touch_j.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">基本指令集</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id2">常用指令集</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="page.html">page-页面跳转指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="prints.html">prints-从串口打印一个变量/常量</a></li>
<li class="toctree-l3"><a class="reference internal" href="printh.html">printh-从串口打印16进制</a></li>
<li class="toctree-l3"><a class="reference internal" href="click.html">click-激活控件的按下/弹起事件</a></li>
<li class="toctree-l3"><a class="reference internal" href="vis.html">vis-隐藏/显示控件</a></li>
<li class="toctree-l3"><a class="reference internal" href="tsw.html">tsw-控件触摸使能</a></li>
<li class="toctree-l3"><a class="reference internal" href="randset.html">randset-随机数范围设置</a></li>
<li class="toctree-l3"><a class="reference internal" href="covx.html">covx-变量类型转换</a></li>
<li class="toctree-l3"><a class="reference internal" href="strlen.html">strlen-字符串变量字符长度测试</a></li>
<li class="toctree-l3"><a class="reference internal" href="btlen.html">btlen-字符串变量字节长度测试</a></li>
<li class="toctree-l3"><a class="reference internal" href="substr.html">substr-字符串截取</a></li>
<li class="toctree-l3"><a class="reference internal" href="spstr.html">spstr-字符串分割</a></li>
<li class="toctree-l3"><a class="reference internal" href="touch_j.html">touch_j-触摸校准</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">add-往曲线控件添加数据</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#add-1">add-示例1</a></li>
<li class="toctree-l4"><a class="reference internal" href="#add-2">add-示例2</a></li>
<li class="toctree-l4"><a class="reference internal" href="#add-3">add-示例3</a></li>
<li class="toctree-l4"><a class="reference internal" href="#add-c">add-c语言示例</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id1">add指令-相关链接</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2">add指令-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="addt.html">addt-曲线数据透传指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="cle.html">cle-清除曲线控件中的数据</a></li>
<li class="toctree-l3"><a class="reference internal" href="rest.html">rest-复位串口屏</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">高级指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id4">文件操作指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id5">不常用指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#crc">CRC校验指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#gui">GUI绘图指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#hmi">HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">指令集</a> &raquo;</li>
      <li>add-往曲线控件添加数据</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="add">
<h1>add-往曲线控件添加数据<a class="headerlink" href="#add" title="此标题的永久链接"></a></h1>
<p>往当前页面的曲线控件添加数据，不支持跨页面添加曲线控件数据</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>add objid,ch,val

objid:曲线控件ID序号(此处必须是ID号，不支持使用控件名称)，推荐使用s0.id这种形式

ch:曲线控件通道号（范围：0-3）

val:数据 (最小0，最大255，还取决于曲线控件的实际高度，例如曲线控件实际高200像素
，当你添加的值超过200的部分就显示不出来。超过255软件会自动取低8位，即对255取余)
</pre></div>
</div>
<section id="add-1">
<h2>add-示例1<a class="headerlink" href="#add-1" title="此标题的永久链接"></a></h2>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>请注意,操作的是s0.id而不是s0</p>
</div>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//往s0曲线控件的0通道添加数据30</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">add</span><span class="w"> </span><span class="n">s0</span><span class="p">.</span><span class="n">id</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">30</span><span class="w"></span>
</pre></div>
</div>
<p>使用s0.id的好处:避免因为置顶/置底或者删除控件导致id改变从而出错</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>只触发一次按钮的话，无法明显观测到波形，需要点击按钮控件多次触发指令才会出现明显的波形</p>
</div>
<img alt="../_images/add_1.jpg" src="../_images/add_1.jpg" />
<img alt="../_images/add_1_1.jpg" src="../_images/add_1_1.jpg" />
</section>
<section id="add-2">
<h2>add-示例2<a class="headerlink" href="#add-2" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//往s0曲线控件的1通道添加数据n0.val，此时波形控件的ch属性至少为2</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">add</span><span class="w"> </span><span class="n">s0</span><span class="p">.</span><span class="n">id</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
</pre></div>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>只触发一次按钮的话，无法明显观测到波形，需要点击按钮控件多次触发指令才会出现明显的波形</p>
</div>
<img alt="../_images/add_2.jpg" src="../_images/add_2.jpg" />
<img alt="../_images/add_2_1.jpg" src="../_images/add_2_1.jpg" />
</section>
<section id="add-3">
<h2>add-示例3<a class="headerlink" href="#add-3" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//往s0曲线控件的0通道随机添加数据</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">add</span><span class="w"> </span><span class="n">s0</span><span class="p">.</span><span class="n">id</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="n">rand</span><span class="w"></span>
</pre></div>
</div>
<p>在页面的前初始化事件中设置随机数范围</p>
<img alt="../_images/add_3.jpg" src="../_images/add_3.jpg" />
<p>在定时器中定时添加随机数到曲线波形控件中</p>
<img alt="../_images/add_4.jpg" src="../_images/add_4.jpg" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>曲线数据只支持8位数据，最小0，最大255。</p>
<p>每个page页面最多支持4个曲线控件,每个曲线控件最多支持4个通道。可以连续发送数据，控件会自动平推显示数据.在发送数据的过程中也可以随时修改控件属性，比如随时修改各个通道的前景色或背景色。</p>
<p>当曲线波形控件的ch属性为1（通道数量为1）时，只有一个通道0可以使用，而不是通道1</p>
</div>
</section>
<section id="add-c">
<h2>add-c语言示例<a class="headerlink" href="#add-c" title="此标题的永久链接"></a></h2>
<p>单片机使用add指令向串口屏添加100个点</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="k">for</span><span class="p">(</span><span class="kt">int</span><span class="w"> </span><span class="n">i</span><span class="w"> </span><span class="o">=</span><span class="mi">0</span><span class="p">;</span><span class="n">i</span><span class="o">&lt;</span><span class="mi">100</span><span class="p">;</span><span class="n">i</span><span class="o">++</span><span class="p">)</span><span class="w"></span>
<span class="linenos">2</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">3</span><span class="w">     </span><span class="c1">//向曲线s0的通道0传输1个数据,add指令不支持跨页面</span>
<span class="linenos">4</span><span class="w">     </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;add s0.id,0,%d</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">,(</span><span class="kt">int</span><span class="p">)(</span><span class="n">rand</span><span class="p">()</span><span class="w"> </span><span class="o">%</span><span class="w"> </span><span class="mi">256</span><span class="p">));</span><span class="w"></span>
<span class="linenos">5</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id1">
<h2>add指令-相关链接<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../widgets/Waveform.html#id1"><span class="std std-ref">曲线波形控件</span></a></p>
<p><a class="reference internal" href="../widgets/Waveform.html#add"><span class="std std-ref">使用add指令时屏幕没有任何反应</span></a></p>
</section>
<section id="id2">
<h2>add指令-样例工程下载<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/基本指令集/add指令/add指令.HMI">《add指令》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="touch_j.html" class="btn btn-neutral float-left" title="touch_j-触摸校准" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="addt.html" class="btn btn-neutral float-right" title="addt-曲线数据透传指令" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>