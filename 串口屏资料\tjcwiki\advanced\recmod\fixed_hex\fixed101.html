<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>例101：帧头为55 aa，定长hex-点灯1 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/translations.js"></script>
        <script src="../../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../../_static/custom.js"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../../genindex.html" />
    <link rel="search" title="搜索" href="../../../search.html" />
    <link rel="next" title="例102：帧头为55 aa，定长hex-点灯2" href="fixed102.html" />
    <link rel="prev" title="例11：有两种帧头为0x47和0x4E ，帧尾为0x0d，0x0a，每帧长度22字节" href="fixed11.html" />
    <link href="../../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../../index.html">串口屏高级应用详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../../keyboard/index.html">系统键盘</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="../index.html">主动解析模式应用详解</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="../recmod_base.html">主动解析基本知识</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html">解析定长hex格式指令-自定义协议</a><ul class="current">
<li class="toctree-l4"><a class="reference internal" href="fixed1.html">例1：帧头为0x55,帧尾为3个0xff,每次传输灯的序号和对应的状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed2.html">例2：帧头为0x55 ，帧尾为3个0xff，一次性传输4个灯的状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed3.html">例3：帧头为0x33 ，帧尾为0d 0a，每次传输灯的序号和对应的状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed4.html">例4：没有帧头 ，帧尾为0d 0a，每次传输灯的序号和对应的状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed5.html">例5：帧头为0x33 ，没有帧尾，每次传输灯的序号和对应的状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed6.html">例6：没有帧头，帧尾为3个0xff，每次传输4个灯的状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed7.html">例7：帧头为0x55，没有帧尾，每次传输4个灯的状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed8.html">例8：没有帧头也没有帧尾，每次传输4个灯的状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed9.html">例9：一个字节有8位，其实可以用1个字节传输8个灯的开关状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed10.html">例10：帧头为0x33 ，无帧尾，使用modbus_crc16校验，一次性传输4个灯的状态</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed11.html">例11：有两种帧头为0x47和0x4E ，帧尾为0x0d，0x0a，每帧长度22字节</a></li>
<li class="toctree-l4 current"><a class="current reference internal" href="#">例101：帧头为55 aa，定长hex-点灯1</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed102.html">例102：帧头为55 aa，定长hex-点灯2</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed103.html">例103：定长hex-主动解析样例工程</a></li>
<li class="toctree-l4"><a class="reference internal" href="fixed104.html">例104：电化学氧气模组SC-03-O2-主动解析样例工程</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../unfixed_hex/index.html">解析不定长hex格式指令-自定义协议</a></li>
<li class="toctree-l3"><a class="reference internal" href="../recmod_ascii/index.html">解析字符串格式指令</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../hmi_download_protocol.html">HMI下载协议详解/OTA升级</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc.html">串口指令增加CRC校验</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc2.html">程序中使用CRC校验数据</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../transmit_data.html">运行中串口传输文件到内存或SD卡</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../SDcard.html">SD卡读写文件流程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../expandIO.html">拓展IO资料</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../get_property.html">获取控件自身的属性</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../wepo_repo.html">读写用户存储区</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getv.html">获取串口屏设备唯一序列号</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../../index.html">串口屏高级应用详解</a> &raquo;</li>
          <li><a href="../index.html">主动解析模式应用详解</a> &raquo;</li>
          <li><a href="index.html">解析定长hex格式指令-自定义协议</a> &raquo;</li>
      <li>例101：帧头为55 aa，定长hex-点灯1</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="aa-hex-1">
<h1>例101：帧头为55 aa，定长hex-点灯1<a class="headerlink" href="#aa-hex-1" title="此标题的永久链接"></a></h1>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>每个页面仅建议在一个定时器中读取串口缓冲区的数据,在多个定时器中读取串口缓冲区的数据容易照成逻辑混乱。</p>
</div>
<p>新建一个空白工程</p>
<p>在工程中新建一个定时器tmDecode，tim设置为50，en设置为1，用于定时解析串口数据</p>
<p>图片控件p0和p1用来显示两个灯的状态</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>16进制发送时，可以忽略大小写，ff和FF是一样的，AA和aa是一样的，但是请不要写成Ff，Ab这样大小写混用</p>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>55 aa 01 01 0d 0a  //左边图片变红（id为1的控件使用id为1的图片）

55 aa 02 00 0d 0a  //右边图片变黑（id为2的控件使用id为0的图片）

55 aa 01 02 0d 0a  //左边图片变黄（id为1的控件使用id为2的图片）

55 aa 02 03 0d 0a  //右边图片变蓝（id为2的控件使用id为3的图片）
</pre></div>
</div>
<p>program.s中的配置如图所示</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//以下代码只在上电时运行一次,一般用于全局变量定义和上电初始化数据
<span class="linenos">2</span>//全局变量定义目前仅支持4字节有符号整形(int),不支持其他类型的全局变量声明,如需使用字符串类型可以在页面中使用变量控件来实现
<span class="linenos">3</span>int sys0=0,sys1=0,sys2=0
<span class="linenos">4</span>//frameLength：每帧数据长度
<span class="linenos">5</span>//getFrameFlag：是否找到帧头或帧尾标记
<span class="linenos">6</span>int frameLength=6,getFrameFlag
<span class="linenos">7</span>bauds=115200 //波特率115200
<span class="linenos">8</span>recmod=1    //打开主动解析
<span class="linenos">9</span>page 0                       //上电刷新第0页
</pre></div>
</div>
<p>解析定时器（tim为50）中的代码如下图所示</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>while(usize&gt;=frameLength&amp;&amp;getFrameFlag==0)
{
  if(u[0]==0x55&amp;&amp;u[1]==0xaa&amp;&amp;u[4]==0x0d&amp;&amp;u[5]==0x0a)//包头为0x55aa，并且以0d0a结尾
  {
    //找到帧头,退出循环
    getFrameFlag=1
  }else
  {
    //如果帧头不对，就一直删除1个字节，直到不满足条件退出循环
    udelete 1
  }
}
if(getFrameFlag==1)
{
  b[u[2]].pic=u[3]
  //下面将串口缓冲区的各个数据写到屏幕的控件中方便观看
  sys2=0
  for(sys1=n0.id;sys1&lt;=n5.id;sys1++)
  {
    b[sys1].val=u[sys2]
    sys2++
  }
  udelete frameLength //删除已经解析过的数据
  getFrameFlag=0 //清空标记变量
}
</pre></div>
</div>
<section id="hex-1">
<h2>定长hex-点灯1-样例工程下载<a class="headerlink" href="#hex-1" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/主动解析/定长hex-点灯1.HMI">《定长hex-点灯1》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="fixed11.html" class="btn btn-neutral float-left" title="例11：有两种帧头为0x47和0x4E ，帧尾为0x0d，0x0a，每帧长度22字节" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="fixed102.html" class="btn btn-neutral float-right" title="例102：帧头为55 aa，定长hex-点灯2" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>