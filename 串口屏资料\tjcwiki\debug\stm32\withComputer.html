<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>stm32说明 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="stm32 HAL库开发" href="hal_lib/index.html" />
    <link rel="prev" title="与stm32单片机联调" href="index.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../start/index.html">快速入门</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">串口屏调试</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../simulator/index.html">模拟器调试基础知识</a></li>
<li class="toctree-l2"><a class="reference internal" href="../connect_ttl.html">和USB转TTL的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="../connect_mcu.html">和单片机的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="../base/index.html">联机调试基础知识</a></li>
<li class="toctree-l2"><a class="reference internal" href="../usart_protocol/index.html">串口屏通讯协议</a></li>
<li class="toctree-l2"><a class="reference internal" href="../debug_with_sscom.html">串口助手软件(sscom)和屏幕联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../debug_with_vspd.html">串口助手软件和串口屏模拟器联调1</a></li>
<li class="toctree-l2"><a class="reference internal" href="../debug_with_vspd2.html">串口助手软件和串口屏模拟器联调2</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">与stm32单片机联调</a><ul class="current">
<li class="toctree-l3 current"><a class="current reference internal" href="#">stm32说明</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id1">stm32通讯-如何下载工程到串口屏</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2">stm32单片机与串口屏的接法</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id3">单片机与模拟器如何连接</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="hal_lib/index.html">stm32 HAL库开发</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../timcu/index.html">与TI（德州仪器）单片机联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../arduino/index.html">与arduino联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../micropython/index.html">与MicroPython联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="../python/index.html">与python联调</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">串口屏调试</a> &raquo;</li>
          <li><a href="index.html">与stm32单片机联调</a> &raquo;</li>
      <li>stm32说明</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="stm32">
<h1>stm32说明<a class="headerlink" href="#stm32" title="此标题的永久链接"></a></h1>
<p>串口屏怎么下载程序</p>
<p><a class="reference internal" href="../../start/create_project/create_project_9.html#id1"><span class="std std-ref">通过串口下载工程到串口屏</span></a></p>
<p><a class="reference internal" href="../../start/create_project/create_project_9_2.html#tft-tftfiledownload"><span class="std std-ref">使用TFT文件下载助手(TFTFileDownload)通过串口下载工程到串口屏</span></a></p>
<p><a class="reference internal" href="../../start/create_project/create_project_10.html#sd"><span class="std std-ref">通过SD卡下载工程到串口屏</span></a></p>
<p>使用的单片机型号为stm32f103c8t6的最小系统板，兼容淘宝常见款，同时也兼容立创·地阔星STM32F103C8T6开发板</p>
<img alt="../../_images/tb_stm32f103c8t6.jpg" src="../../_images/tb_stm32f103c8t6.jpg" />
<img alt="../../_images/jlc_stm32f103c8t6.jpg" src="../../_images/jlc_stm32f103c8t6.jpg" />
<p>通过手册我们查找到，在未重映射的情况下，PA2为TX，PA3为RX。</p>
<img alt="../../_images/stm32_txrx_table.jpg" src="../../_images/stm32_txrx_table.jpg" />
<section id="id1">
<h2>stm32通讯-如何下载工程到串口屏<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../../start/create_project/create_project_9.html#id1"><span class="std std-ref">通过串口下载工程到串口屏</span></a></p>
<p><a class="reference internal" href="../../start/create_project/create_project_10.html#sd"><span class="std std-ref">通过SD卡下载工程到串口屏</span></a></p>
</section>
<section id="id2">
<h2>stm32单片机与串口屏的接法<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<img alt="../../_images/tjc_to_stm32.png" src="../../_images/tjc_to_stm32.png" />
</section>
<section id="id3">
<h2>单片机与模拟器如何连接<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<p>将单片机通过usb转ttl连接到电脑上与电脑联机进行开发</p>
<p>连接方式如下所示</p>
<img alt="../../_images/stm32_to_computer.png" src="../../_images/stm32_to_computer.png" />
<p>参考实物接法如下</p>
<img alt="../../_images/stm32_to_computer2.jpg" src="../../_images/stm32_to_computer2.jpg" />
<p>驱动安装完成后，应该能够在设备管理器中看到这两个驱动，驱动安装请参考 <a class="reference internal" href="../../start/create_project/create_project_8.html#id1"><span class="std std-ref">安装串口驱动</span></a></p>
<p>一个是串口驱动，建议使用cp2102或者ft232芯片的串口工具，不要使用ch340或者pl2303</p>
<p>串口工具的串口号是com7，记住这个串口号，在你的电脑上可能会是其他串口号，需要使用时根据自己电脑的串口号进行选择即可</p>
<p>一个是调试工具的驱动，我这里使用的时stlink，你也可以使用jlink或者daplink，手里有啥就用啥，但我目前只试过stlink，不保证其他调试工具能用</p>
<p>这两个驱动是没有感叹号的，如果有感叹号，说明没有正确安装驱动，请通过百度搜索如何安装相应的驱动</p>
<img alt="../../_images/device_manager.jpg" src="../../_images/device_manager.jpg" />
<p>在开发期间,可以使用模拟器与单片机进行联机调试</p>
<img alt="../../_images/gb2312_s.jpg" src="../../_images/gb2312_s.jpg" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="与stm32单片机联调" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="hal_lib/index.html" class="btn btn-neutral float-right" title="stm32 HAL库开发" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>