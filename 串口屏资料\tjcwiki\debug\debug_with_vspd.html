<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>串口助手软件和串口屏模拟器联调1 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="串口助手软件和串口屏模拟器联调2" href="debug_with_vspd2.html" />
    <link rel="prev" title="串口助手软件(sscom)和屏幕联调" href="debug_with_sscom.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">串口屏调试</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="simulator/index.html">模拟器调试基础知识</a></li>
<li class="toctree-l2"><a class="reference internal" href="connect_ttl.html">和USB转TTL的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="connect_mcu.html">和单片机的接线说明</a></li>
<li class="toctree-l2"><a class="reference internal" href="base/index.html">联机调试基础知识</a></li>
<li class="toctree-l2"><a class="reference internal" href="usart_protocol/index.html">串口屏通讯协议</a></li>
<li class="toctree-l2"><a class="reference internal" href="debug_with_sscom.html">串口助手软件(sscom)和屏幕联调</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">串口助手软件和串口屏模拟器联调1</a></li>
<li class="toctree-l2"><a class="reference internal" href="debug_with_vspd2.html">串口助手软件和串口屏模拟器联调2</a></li>
<li class="toctree-l2"><a class="reference internal" href="stm32/index.html">与stm32单片机联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="timcu/index.html">与TI（德州仪器）单片机联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="arduino/index.html">与arduino联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="micropython/index.html">与MicroPython联调</a></li>
<li class="toctree-l2"><a class="reference internal" href="python/index.html">与python联调</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">串口屏调试</a> &raquo;</li>
      <li>串口助手软件和串口屏模拟器联调1</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>串口助手软件和串口屏模拟器联调1<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<p>串口通讯工具sscom下载链接：</p>
<p><a class="reference internal" href="../download/tools_download.html#sscom"><span class="std std-ref">sscom串口调试助手下载</span></a></p>
<p>电脑需要装一个虚拟串口工具vspd下载链接：</p>
<p><a class="reference internal" href="../download/tools_download.html#vspd"><span class="std std-ref">虚拟串口工具vspd下载</span></a></p>
<p>打开虚拟串口，添加一对串口，如果已经存在虚拟串口，就不用继续添加了。</p>
<p>在安装好了VSPD之后,添加一对虚拟串口</p>
<img alt="../_images/sscomWithMonitor1.png" src="../_images/sscomWithMonitor1.png" />
<p>添加后如图所示</p>
<img alt="../_images/sscomWithMonitor2.png" src="../_images/sscomWithMonitor2.png" />
<p>进入串口屏调试界面,选择用户mcu输入,串口号选择com1,波特率115200,点击开始</p>
<img alt="../_images/sscomWithMonitor3.png" src="../_images/sscomWithMonitor3.png" />
<p>串口助手请使用SSCOM5.13.1版本，根据实际情况设置端口号和波特率，不要勾选“加回车换行”</p>
<p>打开sscom,串口号选择com2(与com1互为1对串口),波特率115200,点击打开串口</p>
<img alt="../_images/sscomWithMonitor4.png" src="../_images/sscomWithMonitor4.png" />
<p>点击拓展按钮,让侧边栏显示出来</p>
<img alt="../_images/sscomWithMonitor5.png" src="../_images/sscomWithMonitor5.png" />
<p>将第一栏编辑为如下指令,注意去掉前面的勾选</p>
<img alt="../_images/sscomWithMonitor6.png" src="../_images/sscomWithMonitor6.png" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>\xff\xff\xff是结束符</p>
</div>
<p>指令如下:</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> t0.txt<span class="o">=</span><span class="s2">&quot;淘晶驰电子&quot;</span><span class="se">\x</span>ff<span class="se">\x</span>ff<span class="se">\x</span>ff
</pre></div>
</div>
<p>点击右侧按钮即可发送当前指令</p>
<img alt="../_images/sscomWithMonitor7.png" src="../_images/sscomWithMonitor7.png" />
<p>此时右下角可以看到接收到了数据,同时t0文本也变成了淘晶驰电子</p>
<img alt="../_images/sscomWithMonitor8.png" src="../_images/sscomWithMonitor8.png" />
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>用户mcu输入功能可以用来连接其他的串口软件或者单片机</p>
</div>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="debug_with_sscom.html" class="btn btn-neutral float-left" title="串口助手软件(sscom)和屏幕联调" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="debug_with_vspd2.html" class="btn btn-neutral float-right" title="串口助手软件和串口屏模拟器联调2" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>