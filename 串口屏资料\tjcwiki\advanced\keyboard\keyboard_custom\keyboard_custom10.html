<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>进入键盘时显示不同的备注信息 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/translations.js"></script>
        <script src="../../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../../_static/custom.js"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../../genindex.html" />
    <link rel="search" title="搜索" href="../../../search.html" />
    <link rel="next" title="自定义好看的键盘" href="keyboard_custom11.html" />
    <link rel="prev" title="键盘输入回车" href="keyboard_custom9.html" />
    <link href="../../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../../index.html">串口屏高级应用详解</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="../index.html">系统键盘</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="../keyboard_base.html">键盘基础知识</a></li>
<li class="toctree-l3"><a class="reference internal" href="../keyboard_principle.html">键盘的实现原理</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html">自定义键盘</a><ul class="current">
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom1.html">进入键盘时自动将输入框清空</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom2.html">键盘页面默认为拼音输入</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom3.html">键盘页面默认为英文大写</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom4.html">如何让键盘的光标不闪烁</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom5.html">键盘页面限制输入数据的值</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom6.html">在键盘页面按下OK键时将参数通过串口发送出去</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom7.html">键盘只能输入32个字符需要改成能输入更多的字符</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom8.html">不同控件在系统键盘设置好参数存储到用户存储区</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom9.html">键盘输入回车</a></li>
<li class="toctree-l4 current"><a class="current reference internal" href="#">进入键盘时显示不同的备注信息</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom11.html">自定义好看的键盘</a></li>
<li class="toctree-l4"><a class="reference internal" href="index.html#keyboardlink">键盘-相关链接</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../keyboard_problem.html">系统键盘常见问题</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../recmod/index.html">主动解析模式应用详解</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../hmi_download_protocol.html">HMI下载协议详解/OTA升级</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc.html">串口指令增加CRC校验</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc2.html">程序中使用CRC校验数据</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../transmit_data.html">运行中串口传输文件到内存或SD卡</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../SDcard.html">SD卡读写文件流程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../expandIO.html">拓展IO资料</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../get_property.html">获取控件自身的属性</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../wepo_repo.html">读写用户存储区</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getv.html">获取串口屏设备唯一序列号</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../../index.html">串口屏高级应用详解</a> &raquo;</li>
          <li><a href="../index.html">系统键盘</a> &raquo;</li>
          <li><a href="index.html">自定义键盘</a> &raquo;</li>
      <li>进入键盘时显示不同的备注信息</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="keyboardinfo">
<span id="id1"></span><h1>进入键盘时显示不同的备注信息<a class="headerlink" href="#keyboardinfo" title="此标题的永久链接"></a></h1>
<p>用于提示当前用户编辑的具体是哪个控件的参数</p>
<img alt="../../../_images/1_11.jpg" src="../../../_images/1_11.jpg" />
<p>在前初始化事件里去触发控件</p>
<img alt="../../../_images/1_12.jpg" src="../../../_images/1_12.jpg" />
<p>判断loadpageid.val和loadcmpid.val就能知道来自哪个页面和控件的点击</p>
<img alt="../../../_images/1_13.jpg" src="../../../_images/1_13.jpg" />
<p>代码如下</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">if</span><span class="p">(</span><span class="n">loadpageid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="mi">0</span><span class="p">)</span>
<span class="p">{</span>
  <span class="k">if</span><span class="p">(</span><span class="n">loadcmpid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="o">.</span><span class="n">t0</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">showInfo</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;设置page0页面的t0&quot;</span>
  <span class="p">}</span><span class="k">else</span> <span class="k">if</span><span class="p">(</span><span class="n">loadcmpid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="o">.</span><span class="n">t1</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">showInfo</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;设置page0页面的t1&quot;</span>
  <span class="p">}</span><span class="k">else</span> <span class="k">if</span><span class="p">(</span><span class="n">loadcmpid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="o">.</span><span class="n">t2</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">showInfo</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;设置page0页面的t2&quot;</span>
  <span class="p">}</span><span class="k">else</span> <span class="k">if</span><span class="p">(</span><span class="n">loadcmpid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="o">.</span><span class="n">t3</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">showInfo</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;设置page0页面的t3&quot;</span>
  <span class="p">}</span>
<span class="p">}</span><span class="k">else</span> <span class="k">if</span><span class="p">(</span><span class="n">loadpageid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="mi">1</span><span class="p">)</span>
<span class="p">{</span>
  <span class="k">if</span><span class="p">(</span><span class="n">loadcmpid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="n">page1</span><span class="o">.</span><span class="n">t0</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">showInfo</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;设置page1页面的t0&quot;</span>
  <span class="p">}</span><span class="k">else</span> <span class="k">if</span><span class="p">(</span><span class="n">loadcmpid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="n">page1</span><span class="o">.</span><span class="n">t1</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">showInfo</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;设置page1页面的t1&quot;</span>
  <span class="p">}</span><span class="k">else</span> <span class="k">if</span><span class="p">(</span><span class="n">loadcmpid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="n">page1</span><span class="o">.</span><span class="n">t2</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">showInfo</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;设置page1页面的t2&quot;</span>
  <span class="p">}</span><span class="k">else</span> <span class="k">if</span><span class="p">(</span><span class="n">loadcmpid</span><span class="o">.</span><span class="n">val</span><span class="o">==</span><span class="n">page1</span><span class="o">.</span><span class="n">t3</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>
  <span class="p">{</span>
    <span class="n">showInfo</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;设置page1页面的t3&quot;</span>
  <span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<section id="id2">
<h2>进入键盘时显示不同的备注信息-样例工程下载<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/系统键盘/进入键盘时显示不同的备注信息.HMI">《进入键盘时显示不同的备注信息》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="keyboard_custom9.html" class="btn btn-neutral float-left" title="键盘输入回车" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="keyboard_custom11.html" class="btn btn-neutral float-right" title="自定义好看的键盘" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>