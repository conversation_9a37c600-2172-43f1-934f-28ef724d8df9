<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>键盘的实现原理 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="自定义键盘" href="keyboard_custom/index.html" />
    <link rel="prev" title="键盘基础知识" href="keyboard_base.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">串口屏高级应用详解</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html">系统键盘</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="keyboard_base.html">键盘基础知识</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">键盘的实现原理</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id2">系统键盘-常用控件和变量说明</a></li>
<li class="toctree-l4"><a class="reference internal" href="#keyboardothermethod">其他跳转到键盘页面的方法</a></li>
<li class="toctree-l4"><a class="reference internal" href="#keyboardgetvalue">键盘如何获取到原始控件的值</a></li>
<li class="toctree-l4"><a class="reference internal" href="#keyboardsetvalue">输入完成后如何赋值给原始控件</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="keyboard_custom/index.html">自定义键盘</a></li>
<li class="toctree-l3"><a class="reference internal" href="keyboard_problem.html">系统键盘常见问题</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../recmod/index.html">主动解析模式应用详解</a></li>
<li class="toctree-l2"><a class="reference internal" href="../hmi_download_protocol.html">HMI下载协议详解/OTA升级</a></li>
<li class="toctree-l2"><a class="reference internal" href="../crc.html">串口指令增加CRC校验</a></li>
<li class="toctree-l2"><a class="reference internal" href="../crc2.html">程序中使用CRC校验数据</a></li>
<li class="toctree-l2"><a class="reference internal" href="../transmit_data.html">运行中串口传输文件到内存或SD卡</a></li>
<li class="toctree-l2"><a class="reference internal" href="../SDcard.html">SD卡读写文件流程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../expandIO.html">拓展IO资料</a></li>
<li class="toctree-l2"><a class="reference internal" href="../get_property.html">获取控件自身的属性</a></li>
<li class="toctree-l2"><a class="reference internal" href="../wepo_repo.html">读写用户存储区</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getv.html">获取串口屏设备唯一序列号</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">串口屏高级应用详解</a> &raquo;</li>
          <li><a href="index.html">系统键盘</a> &raquo;</li>
      <li>键盘的实现原理</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <div class="admonition attention" id="keyboardprinciple">
<p class="admonition-title">注意</p>
<p>如果您没有修改键盘功能的需求不需要再往下看了，可以查看其它章节</p>
</div>
<section id="id1">
<h1>键盘的实现原理<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<section id="id2">
<h2>系统键盘-常用控件和变量说明<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>①变量loadpageid.val：调用页的页面ID。</p>
<p>②变量loadcmpid.val：调用页的控件ID。</p>
<p>③定时器tm0：让输入框有个光标不断闪烁，如果不需要，控件属性en=0即可。</p>
<p>④变量inputlenth：获取正在使用系统键盘控件字符最大长度。</p>
<p>⑤变量input：输入的数据。</p>
<p>⑥文本show：键盘显示的数据,主要目的是为了让输入框有个闪烁的效果。</p>
</section>
<section id="keyboardothermethod">
<span id="id3"></span><h2>其他跳转到键盘页面的方法<a class="headerlink" href="#keyboardothermethod" title="此标题的永久链接"></a></h2>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>正常情况下是不需要配下面这三条代码的</p>
<p>是为了让其他原本不支持键盘的控件控件也能调用键盘</p>
<p>或者是从其他工程中导入的键盘,因为不是用正常方法添加的键盘,没法通过key属性进行配置,需要使用这个方法</p>
</div>
<p>page到键盘页之前，先对键盘页的loadgageid.val和loadcmpid.val赋值就可以了</p>
<p>一般在文本控件或数字控件的按下事件中进行赋值，键盘名.loadpageid.val=dp，键盘名.loadcmpid.val=当前控件的id</p>
<p>其他的逻辑会自动实现,loadpageid.val表示调用页的页面ID,loadcmpid.val表示调用页的控件ID,然后调用page 指令跳转到键盘页面即可</p>
<p>这样就可以在键盘页面判断跳转过来的控件类型（type属性），然后根据不同类型调用不同代码转换到键盘上进行显示</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">keybdAP</span><span class="o">.</span><span class="n">loadpageid</span><span class="o">.</span><span class="n">val</span><span class="o">=</span><span class="n">dp</span>
<span class="n">keybdAP</span><span class="o">.</span><span class="n">loadcmpid</span><span class="o">.</span><span class="n">val</span><span class="o">=</span><span class="s1">&#39;&amp;id&amp;&#39;</span>
<span class="n">page</span> <span class="n">keybdAP</span>
</pre></div>
</div>
<img alt="../../_images/keyboard1.jpg" src="../../_images/keyboard1.jpg" />
<p>配置键盘的过程其实就是上位机帮我们自动添加了这三条代码。</p>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/系统键盘/其他跳转到键盘页面的方法.HMI">《其他跳转到键盘页面的方法》演示工程下载</a></p>
</section>
<section id="keyboardgetvalue">
<span id="id5"></span><h2>键盘如何获取到原始控件的值<a class="headerlink" href="#keyboardgetvalue" title="此标题的永久链接"></a></h2>
<p>下面的代码为键盘页面的前初始化事件</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>//调用此页之前，先对此页的loadpageid.val和loadcmpid.val赋值就可以了，其他的逻辑本页会自动实现
<span class="linenos"> 2</span>//loadpageid.val表示调用页的页面ID,loadcmpid.val表示调用页的控件ID
<span class="linenos"> 3</span>if(p[loadpageid.val].b[loadcmpid.val].type==54)
<span class="linenos"> 4</span>{
<span class="linenos"> 5</span>  covx p[loadpageid.val].b[loadcmpid.val].val,input.txt,0,0
<span class="linenos"> 6</span>  inputlenth.val=24
<span class="linenos"> 7</span>}else if(p[loadpageid.val].b[loadcmpid.val].type==59)
<span class="linenos"> 8</span>{
<span class="linenos"> 9</span>  inputlenth.val=p[loadpageid.val].b[loadcmpid.val].val
<span class="linenos">10</span>  if(inputlenth.val&lt;0)
<span class="linenos">11</span>  {
<span class="linenos">12</span>    inputlenth.val*=-1
<span class="linenos">13</span>    input.txt=&quot;-&quot;
<span class="linenos">14</span>  }else
<span class="linenos">15</span>  {
<span class="linenos">16</span>    input.txt=&quot;&quot;
<span class="linenos">17</span>  }
<span class="linenos">18</span>  temp.val=1
<span class="linenos">19</span>  for(temp2.val=0;temp2.val&lt;p[loadpageid.val].b[loadcmpid.val].vvs1;temp2.val++)
<span class="linenos">20</span>  {
<span class="linenos">21</span>    temp.val*=10
<span class="linenos">22</span>  }
<span class="linenos">23</span>  temp2.val=inputlenth.val/temp.val//得到整数位
<span class="linenos">24</span>  cov temp2.val,tempstr.txt,0
<span class="linenos">25</span>  input.txt+=tempstr.txt+&quot;.&quot;
<span class="linenos">26</span>  temp2.val=temp2.val*temp.val-inputlenth.val//得到小数位
<span class="linenos">27</span>  if(temp2.val&lt;0)
<span class="linenos">28</span>  {
<span class="linenos">29</span>    temp2.val*=-1
<span class="linenos">30</span>  }
<span class="linenos">31</span>  covx temp2.val,tempstr.txt,p[loadpageid.val].b[loadcmpid.val].vvs1,0
<span class="linenos">32</span>  input.txt+=tempstr.txt
<span class="linenos">33</span>  inputlenth.val=24
<span class="linenos">34</span>}else
<span class="linenos">35</span>{
<span class="linenos">36</span>  input.txt=p[loadpageid.val].b[loadcmpid.val].txt
<span class="linenos">37</span>  inputlenth.val=p[loadpageid.val].b[loadcmpid.val].txt_maxl
<span class="linenos">38</span>  if(p[loadpageid.val].b[loadcmpid.val].type==116)
<span class="linenos">39</span>  {
<span class="linenos">40</span>    show.pw=p[loadpageid.val].b[loadcmpid.val].pw
<span class="linenos">41</span>  }
<span class="linenos">42</span>}
<span class="linenos">43</span>show.txt=input.txt
</pre></div>
</div>
<p>可以看到获取到原始控件的值分为3个步骤</p>
<ol class="arabic simple">
<li><p>通过p[loadpageid.val].b[loadcmpid.val].type判断原始控件的类型是数字控件、虚拟浮点数或者其他，loadpageid.val和loadcmpid.val是如何传进来可以参考 <a class="reference internal" href="#keyboardothermethod"><span class="std std-ref">其他跳转到键盘页面的方法</span></a></p></li>
<li><p>根据不同的控件类型，将数据赋值到input.txt中</p></li>
<li><p>将input.txt赋值给show.txt</p></li>
</ol>
</section>
<section id="keyboardsetvalue">
<span id="id6"></span><h2>输入完成后如何赋值给原始控件<a class="headerlink" href="#keyboardsetvalue" title="此标题的永久链接"></a></h2>
<p>下面的代码为键盘页面的OK按键的弹起事件</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>//调用此页之前，先对此页的loadpageid.val和loadcmpid.val赋值就可以了，其他的逻辑本页会自动实现
<span class="linenos"> 2</span>//loadpageid.val表示调用页的页面ID,loadcmpid.val表示调用页的控件ID
<span class="linenos"> 3</span>if(p[loadpageid.val].b[loadcmpid.val].type==54)
<span class="linenos"> 4</span>{
<span class="linenos"> 5</span>  covx input.txt,p[loadpageid.val].b[loadcmpid.val].val,0,0
<span class="linenos"> 6</span>}else if(p[loadpageid.val].b[loadcmpid.val].type==59)
<span class="linenos"> 7</span>{
<span class="linenos"> 8</span>  covx input.txt,temp.val,0,0
<span class="linenos"> 9</span>  if(temp.val&lt;0)
<span class="linenos">10</span>  {
<span class="linenos">11</span>    temp.val*=-1
<span class="linenos">12</span>  }
<span class="linenos">13</span>  for(temp2.val=0;temp2.val&lt;p[loadpageid.val].b[loadcmpid.val].vvs1;temp2.val++)
<span class="linenos">14</span>  {
<span class="linenos">15</span>    temp.val*=10
<span class="linenos">16</span>  }
<span class="linenos">17</span>  p[loadpageid.val].b[loadcmpid.val].val=temp.val
<span class="linenos">18</span>  strlen input.txt,temp.val
<span class="linenos">19</span>  temp.val--
<span class="linenos">20</span>  while(temp.val&gt;=0)
<span class="linenos">21</span>  {
<span class="linenos">22</span>    substr input.txt,tempstr.txt,temp.val,1
<span class="linenos">23</span>    if(tempstr.txt==&quot;.&quot;)
<span class="linenos">24</span>    {
<span class="linenos">25</span>      substr input.txt,tempstr.txt,temp.val+1,p[loadpageid.val].b[loadcmpid.val].vvs1
<span class="linenos">26</span>      covx tempstr.txt,temp2.val,0,0
<span class="linenos">27</span>      strlen tempstr.txt,temp.val
<span class="linenos">28</span>      while(temp.val&lt;p[loadpageid.val].b[loadcmpid.val].vvs1)
<span class="linenos">29</span>      {
<span class="linenos">30</span>        temp2.val*=10
<span class="linenos">31</span>        temp.val++
<span class="linenos">32</span>      }
<span class="linenos">33</span>      p[loadpageid.val].b[loadcmpid.val].val+=temp2.val
<span class="linenos">34</span>      temp.val=-1
<span class="linenos">35</span>    }
<span class="linenos">36</span>    temp.val--
<span class="linenos">37</span>  }
<span class="linenos">38</span>  substr input.txt,tempstr.txt,0,1
<span class="linenos">39</span>  if(tempstr.txt==&quot;-&quot;)
<span class="linenos">40</span>  {
<span class="linenos">41</span>    p[loadpageid.val].b[loadcmpid.val].val*=-1
<span class="linenos">42</span>  }
<span class="linenos">43</span>}else
<span class="linenos">44</span>{
<span class="linenos">45</span>  p[loadpageid.val].b[loadcmpid.val].txt=input.txt
<span class="linenos">46</span>}
<span class="linenos">47</span>page loadpageid.val
</pre></div>
</div>
<p>可以看到输入完成后赋值给原始控件分为3个步骤</p>
<ol class="arabic simple">
<li><p>通过p[loadpageid.val].b[loadcmpid.val].type判断触发控件的类型是数字控件、虚拟浮点数或者其他，loadpageid.val和loadcmpid.val是如何传进来可以参考 <a class="reference internal" href="#keyboardothermethod"><span class="std std-ref">其他跳转到键盘页面的方法</span></a></p></li>
<li><p>根据不同的控件类型，将input.txt赋值给原始控件</p></li>
<li><p>返回 loadpageid.val所记录的页面id</p></li>
</ol>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="keyboard_base.html" class="btn btn-neutral float-left" title="键盘基础知识" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="keyboard_custom/index.html" class="btn btn-neutral float-right" title="自定义键盘" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>