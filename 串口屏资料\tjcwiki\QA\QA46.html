<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>如何查看编译时输出的信息 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="不同页面之间的同名控件有什么联系" href="QA48.html" />
    <link rel="prev" title="烧录文件提示 open file error" href="QA123.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">常见问题</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#id2">硬件相关</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">软件相关</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id4">错误提示</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id5">其他</a><ul class="current">
<li class="toctree-l3 current"><a class="current reference internal" href="#">如何查看编译时输出的信息</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id2">运行内存占用情况</a></li>
<li class="toctree-l4"><a class="reference internal" href="#flash">FLASH（存储空间）占用情况</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="QA48.html">不同页面之间的同名控件有什么联系</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA51.html">在定时器0里面加入使能定时器1的语句为什么系统不执行</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA52.html">大小端模式</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA53.html">如何删除控件</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA57.html">使用串口下载工程速度慢怎么办</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA58.html">printf()重定向之后，发送命令和结束符</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA59.html">实现自己的printf函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA61.html">单片机如何控制屏幕</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA64.html">虚拟sd卡文件夹如何打开</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA65.html">如何实现页面滑动切换</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA66.html">如何设置页面背景</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA67.html">HMI文件和TFT文件的关系</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA69.html">界面旋转/翻转/方向</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA73.html">串口屏如何制作弹窗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA76.html">utf8字库下如何只选择汉字</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA77.html">TFT文件如何下载到串口屏中</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA78.html">只有屏/tft文件,或者源HMI工程丢了,能反编译出HMI文件吗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA80.html">如何导入导出page文件</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA82.html">屏幕保护制作</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA85.html">二维码控件是gb2312还是utf8编码</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA86.html">修改工程编码为utf8或者GB2312</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA87.html">文本数据显示不完全</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA92.html">键盘无法输入中文</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA93.html">单片机如何判断串口屏有没有接入</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA94.html">实现按键锁定功能</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA96.html">串口屏的串口缓冲区大小</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA97.html">主动解析下如何提取负数</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA100.html">带外壳产品安装示意图</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA101.html">显示比例不对，正方形变成长方形，圆型变成椭圆</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA103.html">发送中文汉字相关问题</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA105.html">为什么关闭小板上的电源时，RX灯会亮起</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA106.html">变量控件名和页面名称可以一样吗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA107.html">如何使用TFT文件下载助手(TFTFileDownload)下载工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA110.html">单片机3.3V的串口可以直接接串口屏的串口吗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA114.html">如何安装新字体</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA115.html">如何导入字库</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA116.html">如何导入图片</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA117.html">如何导入动画</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA118.html">如何导入视频</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA119.html">如何导入音频</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA120.html">串口屏连接单片机开发板注意事项</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA121.html">字符编码相关详解——串口怎么发送字符和汉字</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA122.html">为什么转换后的图片/视频/音频体积变大了</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA124.html">如何测量屏幕的尺寸大小</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA130.html">MAC电脑如何开发淘晶驰串口屏</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA131.html">数据记录文件结构分析</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA132.html">哪些指令和操作会消耗flash寿命</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">常见问题</a> &raquo;</li>
      <li>如何查看编译时输出的信息</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>如何查看编译时输出的信息<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<section id="id2">
<h2>运行内存占用情况<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>1.在program.s中定义的变量以及vscope被设置为全局的变量会占用全局内存</p>
<img alt="../_images/QA46_1.jpg" src="../_images/QA46_1.jpg" />
<p>2.每个页面的内存占用=全局内存占用+私有内存占用。当切换页面时，私有变量会被释放，全局变量则不会。任意一个页面占用的内存大小不得超过总可用内存。</p>
<p>以X系列为例，总可用内存524288字节=512kB，running页面占用内存最多，但仅为7868字节，远远没有超过524288字节</p>
<p>正常情况下，X系列的运行内存是完全足够的，T系列和K系列因为仅有3.5K内存，在控件较多的情况下可能不够用</p>
<img alt="../_images/QA46_2.jpg" src="../_images/QA46_2.jpg" />
</section>
<section id="flash">
<h2>FLASH（存储空间）占用情况<a class="headerlink" href="#flash" title="此标题的永久链接"></a></h2>
<p>TFT文件大小 = 图片 + 字库 + 动画 + 视频 + 音频 + 代码</p>
<p>文件大小为最终输出的tft文件的大小，应略小于屏幕的FLASH大小，否则会提示 <a class="reference internal" href="QA44.html#file-is-too-large-for-destination-device"><span class="std std-ref">报错:file is too large for destination device</span></a></p>
<p>相关链接</p>
<p><a class="reference internal" href="QA77.html#tft"><span class="std std-ref">TFT文件如何下载到串口屏中</span></a></p>
<p><a class="reference internal" href="../start/create_project/create_project_10.html#tft"><span class="std std-ref">如何输出TFT生产文件</span></a></p>
<img alt="../_images/QA46_3.jpg" src="../_images/QA46_3.jpg" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="QA123.html" class="btn btn-neutral float-left" title="烧录文件提示 open file error" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="QA48.html" class="btn btn-neutral float-right" title="不同页面之间的同名控件有什么联系" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>