<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>定时器控件 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="变量控件" href="Variable.html" />
    <link rel="prev" title="滑块控件" href="Slider.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">控件详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="component.html">认识控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Page.html">页面控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Text.html">文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Scrolling_text.html">滚动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Number.html">数字控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Xfloat.html">虚拟浮点数控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Button.html">按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Progress_bar.html">进度条控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Picture.html">图片控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Crop.html">切图控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Hotspot.html">触摸热区控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TouchCap.html">触摸捕捉控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gauge.html">指针控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Waveform.html">曲线波形控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Slider.html">滑块控件</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">定时器控件</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id3">定时器控件-使用详解</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id4">关闭定时器</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">打开定时器</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id6">定时器最大时间只能设置为65534(65秒多),怎么计时更长时间</a></li>
<li class="toctree-l4"><a class="reference internal" href="#rtc">通过定时器定时刷新RTC到数字控件上</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7">两秒后跳转到其他页面</a></li>
<li class="toctree-l4"><a class="reference internal" href="#n011">让n0控件每隔1秒自动增加1</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id8">使用定时器配合图片控件循环播放图片</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id9">每个页面限制12个定时器，不够用怎么办</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id10">同一页面两个时间相同的定时器，谁先执行</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#id11">定时器控件-样例工程下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id15">定时器控件-相关链接</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id16">定时器控件-属性详解</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="Variable.html">变量控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Dual-state_button.html">双态按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Checkbox.html">复选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Radio.html">单选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="QRcode.html">二维码控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Switch.html">状态开关控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ComboBox.html">下拉框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TextSelect.html">选择文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="SLText.html">滑动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="DataRecord.html">数据记录控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileBrowser.html">文件浏览器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileStream.html">文件流控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gmov.html">动画控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Video.html">视频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Audio.html">音频控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ExPicture.html">外部图片控件</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">所有控件详解</a> &raquo;</li>
      <li>定时器控件</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>定时器控件<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>视频教程： <a class="reference external" href="https://www.bilibili.com/video/BV1hr421F7no">定时器控件</a></p>
</div>
<p>定时器控件用于定时执行某些代码，或者延时执行某些代码，当定时被使能时，定时器里的代码会定时执行。</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>1、每个页面的定时器数量不能超过12个。</p>
<p>2、定时器只能在当前页面运行，不可后台运行，如果想要定时器一直运行，每个页面都加个定时器。</p>
<p>3、定时器最小的定时时间为50ms，定时器的精度会随代码的复杂程度变化，会有一定的误差。</p>
<p>4、由于每个人的电脑性能不同，模拟器中定时器的效果可能会与实物有较大的差距，电脑上可能会运行得很准，但是串口屏实物上会偏慢，与屏幕内代码的复杂程度有关，请以实物为准。</p>
<p>5、请不要使用定时器来替代RTC功能，不带RTC的型号，使用定时器来实现是完全不现实的。</p>
<p>6、串口屏进入休眠（睡眠）模式后，定时器会停止运行，退出休眠（睡眠）模式后，定时器会继续运行。</p>
</div>
<p>定时器控件位于特殊控件栏上。</p>
<img alt="../_images/timer_1.jpg" src="../_images/timer_1.jpg" />
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>导入字库请参考:  <a class="reference internal" href="../QA/QA115.html#id1"><span class="std std-ref">如何导入字库</span></a></p>
<p>导入图片请参考:  <a class="reference internal" href="../QA/QA116.html#id1"><span class="std std-ref">如何导入图片</span></a></p>
<p>导入动画请参考:  <a class="reference internal" href="../QA/QA117.html#id1"><span class="std std-ref">如何导入动画</span></a></p>
<p>导入视频请参考:  <a class="reference internal" href="../QA/QA118.html#id1"><span class="std std-ref">如何导入视频</span></a></p>
<p>导入音频请参考:  <a class="reference internal" href="../QA/QA119.html#id1"><span class="std std-ref">如何导入音频</span></a></p>
</div>
<section id="id3">
<h2>定时器控件-使用详解<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<section id="id4">
<h3>关闭定时器<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h3>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> <span class="n">tm0</span><span class="o">.</span><span class="n">en</span><span class="o">=</span><span class="mi">0</span>
</pre></div>
</div>
</section>
<section id="id5">
<h3>打开定时器<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h3>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="o">//</span><span class="n">打开定时器</span>
<span class="linenos">2</span><span class="n">tm0</span><span class="o">.</span><span class="n">en</span><span class="o">=</span><span class="mi">1</span>
</pre></div>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>在定时器开启的情况下，对定时器的任意属性赋值（设置tim属性或者en属性），都会导致定时器重新计时。</p>
<p>例如一个定时时间为1000ms（1秒）的定时器tm0，另一个定时时间为50ms的定时器tm1中，在定时器tm1中不断执行tm0.en=1，那么tm0将永远没有执行的机会</p>
<p>若不想定时器重新计时，请使用以下操作：</p>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span> //判断定时器是否打开，如果没打开，则打开
<span class="linenos">2</span> if(tm0.en!=1)
<span class="linenos">3</span> {
<span class="linenos">4</span>     tm0.en=1
<span class="linenos">5</span> }
</pre></div>
</div>
<p>相关链接</p>
<p><a class="reference internal" href="../QA/QA51.html#id1"><span class="std std-ref">在定时器0里面加入使能定时器1的语句为什么系统不执行</span></a></p>
</section>
<section id="id6">
<h3>定时器最大时间只能设置为65534(65秒多),怎么计时更长时间<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h3>
<p>例:计时1小时</p>
<p>新建一个定时器tm0，tim属性设置为1000，新建一个变量n0，用于计时</p>
<p>在定时器tm0的定时事件编写以下代码</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">n0</span><span class="o">.</span><span class="n">val</span><span class="o">++</span>
<span class="o">//</span><span class="mi">3600</span><span class="n">秒</span><span class="p">,</span><span class="n">即1小时</span>
<span class="k">if</span><span class="p">(</span><span class="n">n0</span><span class="o">.</span><span class="n">val</span><span class="o">&gt;=</span><span class="mi">3600</span><span class="p">)</span>
<span class="p">{</span>
   <span class="o">//</span><span class="n">计时时间到</span><span class="p">,</span><span class="n">复位用于计时的变量</span>
   <span class="n">n0</span><span class="o">.</span><span class="n">val</span><span class="o">=</span><span class="mi">0</span>
   <span class="o">//</span><span class="n">其他需要执行的代码</span>

<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="rtc">
<h3>通过定时器定时刷新RTC到数字控件上<a class="headerlink" href="#rtc" title="此标题的永久链接"></a></h3>
<p>定时器的tim设置为300(定时器每300ms执行一次定时事件中的代码)，en设置为1(定时器开启)</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>n0.val<span class="o">=</span>rtc0
<span class="linenos">2</span>n1.val<span class="o">=</span>rtc1
<span class="linenos">3</span>n2.val<span class="o">=</span>rtc2
<span class="linenos">4</span>n3.val<span class="o">=</span>rtc3
<span class="linenos">5</span>n4.val<span class="o">=</span>rtc4
<span class="linenos">6</span>n5.val<span class="o">=</span>rtc5
<span class="linenos">7</span>n6.val<span class="o">=</span>rtc6
</pre></div>
</div>
<img alt="../_images/timer_2.jpg" src="../_images/timer_2.jpg" />
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>为什么将定时器设置为300ms？</p>
<p>因为300ms可以相对均匀的显示时间变化，不会占用太多性能（定时器定时时间越短，会越频繁进入定时器，占用更多单片机的性能）</p>
</div>
</section>
<section id="id7">
<h3>两秒后跳转到其他页面<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h3>
<p>定时器的tim设置为2000(定时器每2000ms执行一次定时事件中的代码)，en设置为1(定时器开启)</p>
<p>定时器中的代码为</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>page page1   //跳转到page1
</pre></div>
</div>
<img alt="../_images/timer_3.jpg" src="../_images/timer_3.jpg" />
</section>
<section id="n011">
<h3>让n0控件每隔1秒自动增加1<a class="headerlink" href="#n011" title="此标题的永久链接"></a></h3>
<p>定时器的tim设置为1000(定时器每1000ms执行一次定时事件中的代码)，en设置为1(定时器开启)</p>
<p>定时器中的代码为</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>n0.val++
</pre></div>
</div>
<img alt="../_images/timer_4.jpg" src="../_images/timer_4.jpg" />
</section>
<section id="id8">
<h3>使用定时器配合图片控件循环播放图片<a class="headerlink" href="#id8" title="此标题的永久链接"></a></h3>
<p>定时器的tim设置为100(定时器每100ms执行一次定时事件中的代码)，en设置为1(定时器开启)</p>
<p>假设图片id从10-20</p>
<p>定时器中的代码为</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="k">if</span><span class="o">(</span>p0.pic&lt;<span class="m">20</span><span class="o">)</span>
<span class="linenos">2</span><span class="o">{</span>
<span class="linenos">3</span>     p0.pic++
<span class="linenos">4</span><span class="o">}</span><span class="k">else</span>
<span class="linenos">5</span><span class="o">{</span>
<span class="linenos">6</span>     p0.pic<span class="o">=</span><span class="m">10</span>
<span class="linenos">7</span><span class="o">}</span>
</pre></div>
</div>
<img alt="../_images/timer_5.jpg" src="../_images/timer_5.jpg" />
</section>
<section id="id9">
<h3>每个页面限制12个定时器，不够用怎么办<a class="headerlink" href="#id9" title="此标题的永久链接"></a></h3>
<p>很多时候，多个定时器是可以和在一起的，比如我现在有3个定时事件，一个是10秒执行一次，一个是30秒执行一次，一个是60秒执行一次，那需要创建3个定时器吗，其实并不需要，只需要创建一个就够了</p>
<p>需要新建一个定时器tm0，一个数字控件n0，三个文本控件t0，t1，t2</p>
<p>tm0控件的tim属性设置为1000</p>
<p>定时器中的代码为</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>t0.txt<span class="o">=</span><span class="s2">&quot;&quot;</span>
<span class="linenos"> 2</span>t1.txt<span class="o">=</span><span class="s2">&quot;&quot;</span>
<span class="linenos"> 3</span>t2.txt<span class="o">=</span><span class="s2">&quot;&quot;</span>
<span class="linenos"> 4</span>n0.val++
<span class="linenos"> 5</span>//因为不能在if里计算,我们需要提前计算好,在if中进行判断
<span class="linenos"> 6</span><span class="nv">sys0</span><span class="o">=</span>n0.val%10
<span class="linenos"> 7</span><span class="nv">sys1</span><span class="o">=</span>n0.val%30
<span class="linenos"> 8</span><span class="nv">sys2</span><span class="o">=</span>n0.val%60
<span class="linenos"> 9</span><span class="k">if</span><span class="o">(</span><span class="nv">sys0</span><span class="o">==</span><span class="m">0</span><span class="o">)</span>
<span class="linenos">10</span><span class="o">{</span>
<span class="linenos">11</span>   //每10秒需要做的事
<span class="linenos">12</span>   t0.txt<span class="o">=</span><span class="s2">&quot;10s&quot;</span>
<span class="linenos">13</span><span class="o">}</span>
<span class="linenos">14</span><span class="k">if</span><span class="o">(</span><span class="nv">sys1</span><span class="o">==</span><span class="m">0</span><span class="o">)</span>
<span class="linenos">15</span><span class="o">{</span>
<span class="linenos">16</span>   //每30秒需要做的事
<span class="linenos">17</span>   t1.txt<span class="o">=</span><span class="s2">&quot;30s&quot;</span>
<span class="linenos">18</span><span class="o">}</span>
<span class="linenos">19</span><span class="k">if</span><span class="o">(</span><span class="nv">sys2</span><span class="o">==</span><span class="m">0</span><span class="o">)</span>
<span class="linenos">20</span><span class="o">{</span>
<span class="linenos">21</span>   //每60秒需要做的事
<span class="linenos">22</span>   t2.txt<span class="o">=</span><span class="s2">&quot;60s&quot;</span>
<span class="linenos">23</span><span class="o">}</span>
</pre></div>
</div>
</section>
<section id="id10">
<h3>同一页面两个时间相同的定时器，谁先执行<a class="headerlink" href="#id10" title="此标题的永久链接"></a></h3>
<p>根据定时器控件id大小决定，id小的先执行</p>
</section>
</section>
<section id="id11">
<h2>定时器控件-样例工程下载<a class="headerlink" href="#id11" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/定时器控件/定时器控件.HMI">《定时器控件》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/定时器控件/超过范围时提示超量程.HMI">《超过范围时提示超量程》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/定时器控件/定时器和图片控件实现动画.HMI">《定时器和图片控件实现动画》演示工程下载</a></p>
</section>
<section id="id15">
<h2>定时器控件-相关链接<a class="headerlink" href="#id15" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../QA/QA10.html#id1"><span class="std std-ref">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</span></a></p>
<p><a class="reference internal" href="../QA/QA11.html#txtval"><span class="std std-ref">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</span></a></p>
<p><a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
<p><a class="reference internal" href="../download/moreProject/tjcwiki_setTime.html#id1"><span class="std std-ref">显示时间和设置时间样例工程</span></a></p>
<p><a class="reference internal" href="../download/moreProject/tjcwiki_time_count2.html#id1"><span class="std std-ref">串口屏跨页面计时样例工程</span></a></p>
</section>
<section id="id16">
<h2>定时器控件-属性详解<a class="headerlink" href="#id16" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="component.html#id6"><span class="std std-ref">控件属性解析</span></a></p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>绿色属性可以通过上位机或者串口屏指令进行修改，黑色属性只能在上位机中修改或者不可修改，可通过上位机进行修改指“选中控件后通过属性栏修改控件的属性”</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">type属性</span></code> -控件类型，固定值，不同类型的控件type值不同，相同类型的控件type值相同，可读，不可通过上位机修改，不可通过指令修改。参考： <a class="reference internal" href="component.html#id10"><span class="std std-ref">控件属性-控件id对照表</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">id属性</span></code> -控件ID，可通过上位机左上角的上下箭头置顶或置底，可读，可通过上位机修改左上角的箭头置顶或置地间接修改，不可通过指令修改。参考： <a class="reference internal" href="../QA/QA28.html#id1"><span class="std std-ref">如何更改控件的前后图层关系</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">objname属性</span></code> -控件名称。不可读，可通过上位机进行修改，不可通过指令更改。</p>
<p><code class="docutils literal notranslate"><span class="pre">vscope属性</span></code> -内存占用(私有占用只能在当前页面被访问，全局占用可以在所有页面被访问)，当设置为私有时，跳转页面后，该控件占用的内存会被释放，重新返回该页面后该控件会恢复到最初的设置。可读，可通过上位机进行修改，不可通过指令更改。参考：<a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">tim属性</span></code> -定时时间,单位:ms(最小50,最大65534)，可读，可通过上位机进行修改，可通过指令更改。在定时器开启的情况下，对定时器的tim属性赋值，会导致定时器重新计时</p>
<p><code class="docutils literal notranslate"><span class="pre">en属性</span></code> -使能开关:0为关闭,1为开启，可读，可通过上位机进行修改，可通过指令更改。在定时器开启的情况下，对定时器的en属性赋值，会导致定时器重新计时</p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="Slider.html" class="btn btn-neutral float-left" title="滑块控件" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="Variable.html" class="btn btn-neutral float-right" title="变量控件" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>