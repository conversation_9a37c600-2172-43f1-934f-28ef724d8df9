<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>2.工具栏 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../_static/doctools.js"></script>
        <script src="../../_static/translations.js"></script>
        <script src="../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../_static/custom.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../genindex.html" />
    <link rel="search" title="搜索" href="../../search.html" />
    <link rel="next" title="3.工具箱" href="ide_introduce3.html" />
    <link rel="prev" title="1.菜单栏" href="ide_introduce1.html" />
    <link href="../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">快速入门</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../download_ide.html">下载和安装上位机软件</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html">上位机基本功能介绍</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="ide_introduce1.html">1.菜单栏</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">2.工具栏</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce3.html">3.工具箱</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce4.html">4.资源文件窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce5.html">5.界面</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce6.html">6.特殊控件窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce7.html">7.输出窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce8.html">8.事件编辑窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce9.html">9.页面窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce10.html">10.属性窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce11.html">11.调试窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce12.html">12.下载窗口</a></li>
<li class="toctree-l3"><a class="reference internal" href="ide_introduce13.html">13.设备</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../first_test.html">到手测试</a></li>
<li class="toctree-l2"><a class="reference internal" href="../create_project/index.html">创建工程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../codeSpace.html">代码编写</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../index.html">快速入门</a> &raquo;</li>
          <li><a href="index.html">上位机基本功能介绍</a> &raquo;</li>
      <li>2.工具栏</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>2.工具栏<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<p>1.文件操作快捷菜单。</p>
<p>如下图所示，快捷操作功能同文件操作菜单。</p>
<p>分别为打开工程、新建工程、保存当前工程。</p>
<img alt="../../_images/toolBar1.png" src="../../_images/toolBar1.png" />
<p>2.项目操作菜单</p>
<img alt="../../_images/toolBar2.png" src="../../_images/toolBar2.png" />
<dl class="simple">
<dt>2.1 编译</dt><dd><p>对当前的工程文件进行编译操作。并将结果显示在输出窗口中。</p>
</dd>
<dt>2.2 调试</dt><dd><p>对编译成功的项目进行模拟运行操作。该功能可以不需要借助实物串口屏快速的验证项目工程的功能是否符合预期需求。具体调试操作请参考调试窗口。</p>
</dd>
<dt>2.3 下载</dt><dd><p>将编译生成的.tft文件下载到实物串口屏中。</p>
</dd>
</dl>
<p>3.控件编辑菜单</p>
<img alt="../../_images/toolBar3.png" src="../../_images/toolBar3.png" />
<dl class="simple">
<dt>3.1 控件复制</dt><dd><p>对界面编辑区中处于选中状态的按钮复制到粘帖板中。也可以使用Crtl+C快捷键执行相同操作。</p>
</dd>
</dl>
<dl class="simple">
<dt>3.2 控件剪切</dt><dd><p>对界面编辑区中处于选中状态的按钮剪切到粘帖板中。也可以使用Crtl+X快捷键执行相同操作。</p>
</dd>
</dl>
<dl class="simple">
<dt>3.3 控件粘帖</dt><dd><p>将剪切板中的控件粘帖到界面编辑区。也可以使用Crtl+V快捷键执行相同操作。</p>
</dd>
</dl>
<dl class="simple">
<dt>3.4 控件锁定</dt><dd><p>选择控件以后，通过点击锁定/解锁按纽来实现控件的锁定/解锁功能。控件被锁定以后，将再控件的右上角显示一个《锁》图标。
被锁定的控件，不能通过鼠标拖动控件修改控件位置，和鼠标拖动修改控件的大小。但是仍然可以选中控件后通过修改属性区域的x，y，w，h属性来调整控件的位置和大小。该功能能有效防止防止因误操作修改了已经布局好的界面。</p>
</dd>
<dt>3.5 控件解锁</dt><dd><p>解锁被锁定的控件。</p>
</dd>
</dl>
<p>4.控件操作</p>
<img alt="../../_images/toolBar4.png" src="../../_images/toolBar4.png" />
<dl class="simple">
<dt>4.1 控件删除</dt><dd><p>对界面编辑区中处于选中状态的控件执行删除操作。</p>
</dd>
<dt>4.2 控件撤销</dt><dd><p>撤销上一次在界面编辑区的操作。</p>
</dd>
<dt>4.3 控件恢复</dt><dd><p>恢复上一次在界面编辑区的操作。</p>
</dd>
</dl>
<p>5.界面设置</p>
<img alt="../../_images/toolBar5.png" src="../../_images/toolBar5.png" />
<dl class="simple">
<dt>5.1 设备按钮</dt><dd><p>打开设备选择窗口。可以配置产品系列，型号以及屏幕显示方向等。具体请参考本章节 <a class="reference internal" href="ide_introduce13.html#id3"><span class="std std-ref">设备窗口</span></a> 。</p>
</dd>
<dt>5.2 ID按钮</dt><dd><p>点击该按钮，可以切换显示/不显示界面区域控件名称。</p>
</dd>
<dt>5.3 缩放比例</dt><dd><p>可以对界面区域的界面编辑内容进行放大/缩小调节。范围为10%-600%。方便界面布局的时候，精确摆放控件的位置和调节控件的大小。</p>
</dd>
</dl>
<div class="admonition tip">
<p class="admonition-title">小技巧</p>
<p>可以点击当前的百分比数值，快速将显示比例调整为默认的100%。</p>
</div>
<dl class="simple">
<dt>6.控件图层调节</dt><dd><p>图层调节，主要用于在多个控件存在重叠区域时的现实层级关系。一个页面上的所有控件都拥有不同的图层，可以通过图层调节按钮来调节控件的图层关系。</p>
</dd>
</dl>
<img alt="../../_images/toolBar6.png" src="../../_images/toolBar6.png" />
<p>6.1 该按钮 ↑ 用于将控件置于所有控件最顶层。</p>
<p>6.2 该按钮 ↓ 用于将控件置于所有控件最顶层。</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>T0，K0系列串口屏，只有在页面初始化的时候，具体图层功能。再触摸屏运行过程中，低层级的控件刷新后，将被置于顶层。X系列串口屏具有图层保持功能，在运行过程中能一直保持图层级别。</p>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>使用图层调节按钮，调节控件的图层优先级功能会导致控件ID号变化，如果需要用到名称组等功能，需要特别小心！</p>
</div>
<p>7.控件对齐</p>
<img alt="../../_images/toolBar7.png" src="../../_images/toolBar7.png" />
<dl class="simple">
<dt>7.1 左对齐</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础进行左对齐。</p>
</dd>
<dt>7.2 右对齐</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础进行右对齐。</p>
</dd>
<dt>7.3 上对齐</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础进行上对齐。</p>
</dd>
<dt>7.4 下对齐</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础进行下对齐。</p>
</dd>
</dl>
<p>8.控件大小</p>
<img alt="../../_images/toolBar8.png" src="../../_images/toolBar8.png" />
<dl class="simple">
<dt>8.1 使宽度相等</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础进行控件宽度对齐。</p>
</dd>
<dt>8.2 使高度相等</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础进行控件高度对齐。</p>
</dd>
<dt>8.3 使大小相等</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础进行控件大小对齐。</p>
</dd>
</dl>
<p>9.控件水平间距</p>
<img alt="../../_images/toolBar9.png" src="../../_images/toolBar9.png" />
<dl class="simple">
<dt>9.1 使水平间距相等</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础使水平间距相等。</p>
</dd>
<dt>9.2 增加水平间距</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础增加水平间距。</p>
</dd>
<dt>9.3 减小水平间距</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础减小水平间距。</p>
</dd>
</dl>
<p>10.控件垂直间距</p>
<img alt="../../_images/toolBar10.png" src="../../_images/toolBar10.png" />
<dl class="simple">
<dt>10.1 使垂直间距相等</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础使垂直间距相等。</p>
</dd>
<dt>10.2 增加垂直间距</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础增加垂直间距。</p>
</dd>
<dt>10.3 减小垂直间距</dt><dd><p>选中多个控件，蓝色的控件为主控件，所有控件以此控件为基础减小垂直间距。</p>
</dd>
</dl>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="ide_introduce1.html" class="btn btn-neutral float-left" title="1.菜单栏" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="ide_introduce3.html" class="btn btn-neutral float-right" title="3.工具箱" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>