<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>cfgpio-扩展IO模式配置 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="setlayer-运行中改变控件图层顺序" href="setlayer.html" />
    <link rel="prev" title="beep-蜂鸣器鸣叫" href="beep.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">基本指令集</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#id2">常用指令集</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id3">高级指令</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="wepo.html">wepo-写入数据到掉电存储空间</a></li>
<li class="toctree-l3"><a class="reference internal" href="repo.html">repo-从掉电存储空间读取数据</a></li>
<li class="toctree-l3"><a class="reference internal" href="wept.html">wept-通过串口透传数据到掉电存储空间</a></li>
<li class="toctree-l3"><a class="reference internal" href="rept.html">rept-从掉电存储空间读取数据并透传发送到串口</a></li>
<li class="toctree-l3"><a class="reference internal" href="beep.html">beep-蜂鸣器鸣叫</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">cfgpio-扩展IO模式配置</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#cfgpio-1">cfgpio-示例1:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#cfgpio-2">cfgpio-示例2:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#cfgpio-3">cfgpio-示例3:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#cfgpio-4">cfgpio-示例4:</a></li>
<li class="toctree-l4"><a class="reference internal" href="#cfgpio">cfgpio指令-样例工程下载</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id1">cfgpio指令-相关链接</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="setlayer.html">setlayer-运行中改变控件图层顺序</a></li>
<li class="toctree-l3"><a class="reference internal" href="move.html">move-控件移动</a></li>
<li class="toctree-l3"><a class="reference internal" href="play.html">play-音频播放</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id4">文件操作指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id5">不常用指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#crc">CRC校验指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#gui">GUI绘图指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#hmi">HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">指令集</a> &raquo;</li>
      <li>cfgpio-扩展IO模式配置</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="cfgpio-io">
<h1>cfgpio-扩展IO模式配置<a class="headerlink" href="#cfgpio-io" title="此标题的永久链接"></a></h1>
<p>具体的使用方法请参考:ref:<cite>拓展IO资料</cite></p>
<dl>
<dt>支持的型号：</dt><dd><p>x5系列支持8路IO</p>
<p>k0系列支持8路IO</p>
<p>X2系列非COF封装的屏幕支持8路IO</p>
</dd>
</dl>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>cfgpio id,state,obj

id:扩展IO的序号

state:配置模式(0-上拉输入模式,1-控件事件邦定输入模式,2-推挽输出模式,3-PWM输出模式,4-开漏模式)

obj:绑定控件名称或ID(此参数仅在配置为控件事件邦定输入模式下有效，其他模式下无效)
</pre></div>
</div>
<section id="cfgpio-1">
<h2>cfgpio-示例1:<a class="headerlink" href="#cfgpio-1" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//将io0配置为上拉输入</span>
<span class="linenos">2</span><span class="w"> </span><span class="c1">//配置为此模式后，任意时刻可以使用系统变量pio0读取当前输入电平,如:n0.val=pio0</span>
<span class="linenos">3</span><span class="w"> </span><span class="n">cfgpio</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/cfgpio_1.jpg" src="../_images/cfgpio_1.jpg" />
</section>
<section id="cfgpio-2">
<h2>cfgpio-示例2:<a class="headerlink" href="#cfgpio-2" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//将io1配置为推挽输出</span>
<span class="linenos">2</span><span class="w"> </span><span class="c1">//配置为此模式后，任意时刻可以使用系统变量pio1控制当前输出电平，如:pio1=1</span>
<span class="linenos">3</span><span class="w"> </span><span class="n">cfgpio</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="mi">2</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/cfgpio_2.jpg" src="../_images/cfgpio_2.jpg" />
</section>
<section id="cfgpio-3">
<h2>cfgpio-示例3:<a class="headerlink" href="#cfgpio-3" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//将io2配置为控件事件邦定输入，邦定控件为b0</span>
<span class="linenos">2</span><span class="w"> </span><span class="c1">//配置为此模式后，io2产生下降沿的时候将触发b0控件的按下事件，产生上升沿的时候将触发b0控件的弹起事件</span>
<span class="linenos">3</span><span class="w"> </span><span class="n">cfgpio</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="n">b0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/cfgpio_3.jpg" src="../_images/cfgpio_3.jpg" />
</section>
<section id="cfgpio-4">
<h2>cfgpio-示例4:<a class="headerlink" href="#cfgpio-4" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//将io4配置为PWM输出模式，配置之前需要先设置占空比，即系统变量变量中的pwm4</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">cfgpio</span><span class="w"> </span><span class="mi">4</span><span class="p">,</span><span class="mi">3</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/cfgpio_4.jpg" src="../_images/cfgpio_4.jpg" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>K0/X2系列只有io4-io7才支持PWM输出，X5系列只有io7才支持PWM输出 其他IO不支持。配置其他IO为PWM模式会报错。</p>
<p>使用控件事件邦定输入模式时，必须是在当前配置时刻的当前页面的控件才能邦定，不可以邦定其他页面的控件（即使是全局内存占用的控件也不可以），邦定当前页面控件以后，当重新刷新页面或者切换到别的页面后，邦定事件将不会继续触发，因此每次刷新页面需要重新邦定，建议将邦定代码写在页面的前初始化事件中最为合适。</p>
<p>每个需要用到外部IO的页面，都必须在前初始化事件中重新配置一次需要用到的相关IO口</p>
<p>所有系列拓展IO口电平都是3.3v</p>
</div>
</section>
<section id="cfgpio">
<h2>cfgpio指令-样例工程下载<a class="headerlink" href="#cfgpio" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/拓展IO/拓展IO例程.HMI">《拓展IO例程》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/拓展IO/IOtest.HMI">《IOtest》演示工程下载</a></p>
</section>
<section id="id1">
<h2>cfgpio指令-相关链接<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../advanced/expandIO.html#io"><span class="std std-ref">拓展IO资料</span></a></p>
<p><a class="reference internal" href="../QA/QA71.html#io"><span class="std std-ref">拓展IO相关问题</span></a></p>
<p><a class="reference internal" href="../variables/pio0-pio7.html#pio0-pio7-io"><span class="std std-ref">pio0~pio7-扩展IO端口</span></a></p>
<p><a class="reference internal" href="../variables/pwm4-pwm7.html#pwm4-pwm7-io"><span class="std std-ref">pwm4~pwm7-扩展IO占空比</span></a></p>
<p><a class="reference internal" href="../variables/pwmf.html#pwmf-pwm"><span class="std std-ref">pwmf-PWM输出的频率</span></a></p>
<p><a class="reference internal" href="../advanced/expandIO.html#sleepio"><span class="std std-ref">sleep睡眠模式与拓展IO</span></a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="beep.html" class="btn btn-neutral float-left" title="beep-蜂鸣器鸣叫" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="setlayer.html" class="btn btn-neutral float-right" title="setlayer-运行中改变控件图层顺序" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>