<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>0X88 系统启动成功 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="0X89 开始SD卡升级" href="return89FFFFFF.html" />
    <link rel="prev" title="0X87 设备自动唤醒" href="return87FFFFFF.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">串口屏返回数据格式</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#bkcmd0">bkcmd非0时的通知格式</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id2">其他数据返回格式</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="return24FFFFFF.html">24 FF FF FF 串口缓冲区溢出</a></li>
<li class="toctree-l3"><a class="reference internal" href="return65FFFFFF.html">0X65 控件点击事件返回</a></li>
<li class="toctree-l3"><a class="reference internal" href="return66FFFFFF.html">0X66 当前页面的ID号返回</a></li>
<li class="toctree-l3"><a class="reference internal" href="return67FFFFFF.html">0X67 触摸坐标数据返回</a></li>
<li class="toctree-l3"><a class="reference internal" href="return68FFFFFF.html">0X68 睡眠模式触摸事件</a></li>
<li class="toctree-l3"><a class="reference internal" href="return70FFFFFF.html">0X70 字符串变量数据返回</a></li>
<li class="toctree-l3"><a class="reference internal" href="return71FFFFFF.html">0X71 数值变量数据返回</a></li>
<li class="toctree-l3"><a class="reference internal" href="return86FFFFFF.html">0X86 设备自动进入睡眠模式</a></li>
<li class="toctree-l3"><a class="reference internal" href="return87FFFFFF.html">0X87 设备自动唤醒</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">0X88 系统启动成功</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id1">1.65.0版本以前</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2">1.65.0版本以后</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="return89FFFFFF.html">0X89 开始SD卡升级</a></li>
<li class="toctree-l3"><a class="reference internal" href="returnFDFFFFFF.html">0XFD 透传数据完成</a></li>
<li class="toctree-l3"><a class="reference internal" href="returnFEFFFFFF.html">0XFE 数据透传就绪</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">串口屏返回数据格式</a> &raquo;</li>
      <li>0X88 系统启动成功</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="x88">
<h1>0X88 系统启动成功<a class="headerlink" href="#x88" title="此标题的永久链接"></a></h1>
<p>0x88+结束符</p>
<img alt="../_images/return88_1.jpg" src="../_images/return88_1.jpg" />
<p>设备上电初始化成功之后发送此数据</p>
<section id="id1">
<h2>1.65.0版本以前<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>此返回不受bkcmd影响</p>
</div>
</section>
<section id="id2">
<h2>1.65.0版本以后<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>取消设备开机固定发送开机信息，转为Program.s中由代码发送开机信息（用户可自行注释或者删除）</p>
<img alt="../_images/return88_2.jpg" src="../_images/return88_2.jpg" />
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="return87FFFFFF.html" class="btn btn-neutral float-left" title="0X87 设备自动唤醒" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="return89FFFFFF.html" class="btn btn-neutral float-right" title="0X89 开始SD卡升级" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>