<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>视频教程 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
        <script src="_static/jquery.js"></script>
        <script src="_static/underscore.js"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="_static/doctools.js"></script>
        <script src="_static/translations.js"></script>
        <script src="_static/js\rtd_sphinx_search.min.js"></script>
        <script src="_static/custom.js"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="索引" href="genindex.html" />
    <link rel="search" title="搜索" href="search.html" />
    <link rel="next" title="常见问题" href="QA/index.html" />
    <link rel="prev" title="原理图下载" href="download/scheme_download.html" />
    <link href="_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="download/index.html">资料下载</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">视频教程</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#id2">基础入门</a></li>
<li class="toctree-l2"><a class="reference internal" href="#id18">控件详解</a></li>
<li class="toctree-l2"><a class="reference internal" href="#id30">视频对应的工程下载链接</a></li>
<li class="toctree-l2"><a class="reference internal" href="#id31">其他教程</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home"></a> &raquo;</li>
      <li>视频教程</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
<section id="id1">
<h1>视频教程<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<section id="id2">
<h2>基础入门<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p><a class="reference external" href="https://www.bilibili.com/video/BV16T4y127YE">1.上位机软件下载与工程创建</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1Wv411K7z4">2.简单工程的制作</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1YL41147HX">3.串口屏调试</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1hA411c7VN">4.模拟器联机调试</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV13q4y1P7eH">5.arduino控制串口屏</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1Mh41187bN">6.自定义串口协议</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1xq4y137y6">7.arduino解析协议</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1wR4y1t7GQ">8.串口屏常见提示与解决方法</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1sg411A7qm">9.串口屏主动解析</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1BY411p7aX">10.串口屏显示电脑运行信息</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1s3411v7HH">11.网络时钟制作分享</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1wT4y197CG">12.串口屏名称组使用</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1V44y1g7dW">13.串口屏主动解析原理分析</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1LR4y1w7cg">14.键盘高级用法</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1eg411Q7x5">15.如何实现掉电保存</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1y94y1S7mm">16.快速制作好看的界面</a></p>
</section>
<hr class="docutils" />
<section id="id18">
<h2>控件详解<a class="headerlink" href="#id18" title="此标题的永久链接"></a></h2>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1vN4y1279w">认识控件</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1mC4y1u734">页面控件</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1nc411x7zo">文本控件&amp;滚动文本控件</a></p>
<p><a class="reference external" href="ps://www.bilibili.com/video/BV1iZ421h7FF">数字控件&amp;虚拟浮点数控件</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1jA4m1P7kT">按钮控件&amp;双态按钮控件&amp;状态按钮控件</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1Bx4y1i72h">进度条控件&amp;滑块控件</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1hr421F7no">触摸捕捉控件、触摸热区控件和定时器控件</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1PZiPeTECZ">曲线控件&amp;二维码控件</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1GtJqztEyr">文件流控件使用教程一</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1gwJnz3E7R">文件流控件使用教程二</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV18hJxzdEnh">文件流控件使用教程三</a></p>
</section>
<section id="id30">
<h2>视频对应的工程下载链接<a class="headerlink" href="#id30" title="此标题的永久链接"></a></h2>
<p><a class="reference external" href="https://pan.baidu.com/s/1yD0kG5j4HwfME-iOuIDiLw">https://pan.baidu.com/s/1yD0kG5j4HwfME-iOuIDiLw</a></p>
<p>提取码：tjcc</p>
</section>
<hr class="docutils" />
<section id="id31">
<h2>其他教程<a class="headerlink" href="#id31" title="此标题的永久链接"></a></h2>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1Xg4y1z79m">串口屏显示AIDA64数据</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1Jg4y1B79Z">自定义键盘</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1Xt4y117dQ">EEPROM的使用</a></p>
<p><a class="reference external" href="https://www.bilibili.com/video/BV1W2YeeLEif">使用python动态获取奥运会奖牌数据</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="download/scheme_download.html" class="btn btn-neutral float-left" title="原理图下载" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="QA/index.html" class="btn btn-neutral float-right" title="常见问题" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>