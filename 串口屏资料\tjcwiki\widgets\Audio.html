<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>音频控件 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="外部图片控件" href="ExPicture.html" />
    <link rel="prev" title="视频控件" href="Video.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">控件详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="component.html">认识控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Page.html">页面控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Text.html">文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Scrolling_text.html">滚动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Number.html">数字控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Xfloat.html">虚拟浮点数控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Button.html">按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Progress_bar.html">进度条控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Picture.html">图片控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Crop.html">切图控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Hotspot.html">触摸热区控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TouchCap.html">触摸捕捉控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gauge.html">指针控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Waveform.html">曲线波形控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Slider.html">滑块控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Timer.html">定时器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Variable.html">变量控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Dual-state_button.html">双态按钮控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Checkbox.html">复选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Radio.html">单选框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="QRcode.html">二维码控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Switch.html">状态开关控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="ComboBox.html">下拉框控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="TextSelect.html">选择文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="SLText.html">滑动文本控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="DataRecord.html">数据记录控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileBrowser.html">文件浏览器控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="FileStream.html">文件流控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Gmov.html">动画控件</a></li>
<li class="toctree-l2"><a class="reference internal" href="Video.html">视频控件</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">音频控件</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id2">音频控件-使用详解</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id3">打开或关闭音频</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id4">设置音频是否循环播放</a></li>
<li class="toctree-l4"><a class="reference internal" href="#play">音频控件和play指令区别</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id5">如何使用外部音频播放</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#id6">音频控件-常见问题</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#wav0-vid-id">wav0.vid 初始值无效:音频ID无效</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id7">屏幕使用外部音频播放无声音</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#id8">音频控件-样例工程下载</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id12">音频控件-相关链接</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id13">音频控件-属性详解</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="ExPicture.html">外部图片控件</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">所有控件详解</a> &raquo;</li>
      <li>音频控件</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>音频控件<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<p>音频控件用于播放音频，仅带喇叭的型号（例如X3、X5系列）支持</p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>导入字库请参考:  <a class="reference internal" href="../QA/QA115.html#id1"><span class="std std-ref">如何导入字库</span></a></p>
<p>导入图片请参考:  <a class="reference internal" href="../QA/QA116.html#id1"><span class="std std-ref">如何导入图片</span></a></p>
<p>导入动画请参考:  <a class="reference internal" href="../QA/QA117.html#id1"><span class="std std-ref">如何导入动画</span></a></p>
<p>导入视频请参考:  <a class="reference internal" href="../QA/QA118.html#id1"><span class="std std-ref">如何导入视频</span></a></p>
<p>导入音频请参考:  <a class="reference internal" href="../QA/QA119.html#id1"><span class="std std-ref">如何导入音频</span></a></p>
</div>
<section id="id2">
<h2>音频控件-使用详解<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>音频控件位于特殊控件栏</p>
<img alt="../_images/audio_1.jpg" src="../_images/audio_1.jpg" />
<p>使用音频控件前，需要先用音视频转换工具转换音频资源（即使原本就是wav格式，如果导不进去，也请再转换一遍）</p>
<img alt="../_images/audio_2.jpg" src="../_images/audio_2.jpg" />
<p>点击这里切换到音频转换</p>
<img alt="../_images/audio_3.jpg" src="../_images/audio_3.jpg" />
<p>转换好的音频文件，从左下角的音频资源窗口导入</p>
<img alt="../_images/audio_4.jpg" src="../_images/audio_4.jpg" />
<p>导入成功后，我们需要关注的是音频文件的ID号</p>
<img alt="../_images/audio_5.jpg" src="../_images/audio_5.jpg" />
<p>音频控件的vid属性填写的是对应音频控件的id号</p>
<img alt="../_images/audio_6.jpg" src="../_images/audio_6.jpg" />
<img alt="../_images/audio_7.jpg" src="../_images/audio_7.jpg" />
<p>音频控件可以配置from属性，来设置从flash内部或者从SD卡内读取音频资源进行播放，当配置为外部文件时，此时将会从SD卡中调用文件，vid属性将变成path属性</p>
<p>请提前将转换好的资源文件复制到SD卡或者虚拟SD卡文件夹，并且填写正确的path属性。</p>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>当需要在上位机的模拟器里调试时，请将资源复制到虚拟SD卡文件夹中，需要在串口屏实物上调试时，请将资源复制到SD卡里，并将SD卡插到串口屏上</p>
</div>
<p>虚拟SD卡文件夹打开方式</p>
<img alt="../_images/virtualSD.jpg" src="../_images/virtualSD.jpg" />
<p>SD卡不能超过32GB（例如：512M、1GB、2GB、4GB、8GB、16GB、32GB都是可用的），请格式化为FAT32格式</p>
<p>例如放在SD卡根目录的1.wav文件，对应的路径是</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>sd0/1.wav
</pre></div>
</div>
<img alt="../_images/audio_8.jpg" src="../_images/audio_8.jpg" />
<p>放在SD卡music目录下的mylove.wav文件，对应的路径是</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>sd0/music/mylove.wav
</pre></div>
</div>
<img alt="../_images/audio_9.jpg" src="../_images/audio_9.jpg" />
<section id="id3">
<h3>打开或关闭音频<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h3>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>//串口屏控件事件代码（通常写在串口屏控件的按下或弹起事件中）
<span class="linenos"> 2</span>wav0.en=1   //开始播放音频
<span class="linenos"> 3</span>wav0.en=0   //停止播放音频
<span class="linenos"> 4</span>
<span class="linenos"> 5</span>
<span class="linenos"> 6</span>//单片机发送指令音频控制指令
<span class="linenos"> 7</span>int sound_sta = 1;   //播放状态
<span class="linenos"> 8</span>char tjcstr[100];
<span class="linenos"> 9</span>sprintf(tjcstr, &quot;main.wav0.en=%d\xff\xff\xff&quot;,sound_sta);
<span class="linenos">10</span>printf(tjcstr);   //单片机需要配置printf重定向到串口
<span class="linenos">11</span>
<span class="linenos">12</span>
<span class="linenos">13</span>//arduino发送指令音频控制指令
<span class="linenos">14</span>int sound_sta = 1;   //播放状态
<span class="linenos">15</span>char tjcstr[100];
<span class="linenos">16</span>sprintf(tjcstr, &quot;main.wav0.en=%d\xff\xff\xff&quot;,sound_sta);
<span class="linenos">17</span>Serial.print(tjcstr);
</pre></div>
</div>
</section>
<section id="id4">
<h3>设置音频是否循环播放<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h3>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>//串口屏控件事件代码（通常写在串口屏控件的按下或弹起事件中）
<span class="linenos"> 2</span>wav0.loop=1   //循环播放音频
<span class="linenos"> 3</span>wav0.loop=0   //不循环播放音频
<span class="linenos"> 4</span>
<span class="linenos"> 5</span>
<span class="linenos"> 6</span>//单片机发送指令音频控制指令
<span class="linenos"> 7</span>int sound_loop = 1;   //循环状态
<span class="linenos"> 8</span>char tjcstr[100];
<span class="linenos"> 9</span>sprintf(tjcstr, &quot;main.wav0.loop=%d\xff\xff\xff&quot;,sound_loop);
<span class="linenos">10</span>printf(tjcstr);   //单片机需要配置printf重定向到串口
<span class="linenos">11</span>
<span class="linenos">12</span>
<span class="linenos">13</span>//arduino发送指令音频控制指令
<span class="linenos">14</span>int sound_loop = 1;   //循环状态
<span class="linenos">15</span>char tjcstr[100];
<span class="linenos">16</span>sprintf(tjcstr, &quot;main.wav0.loop=%d\xff\xff\xff&quot;,sound_loop);
<span class="linenos">17</span>Serial.print(tjcstr);
</pre></div>
</div>
</section>
<section id="play">
<h3>音频控件和play指令区别<a class="headerlink" href="#play" title="此标题的永久链接"></a></h3>
<p>①音频控件播放音频只能在当前页面播放，不可跨页面播放。play指令可以在各个页面使用。如果设置各个页面按键音那么使用play指令。</p>
<p>②音频控件可以选择播放外部资源文件(sd卡文件)，play指令只能播放内部资源文件。</p>
<p>③音频控件可以获取播放音频的总时间以及当前时间，play指令无法获取。</p>
<p>④音频控件会占用大量内存，使用多个音频控件会导致内存溢出。play指令不会出现类似问题。</p>
<p>⑤音频控件在事件编译窗口会有播放完成事件，play指令没有。</p>
</section>
<section id="id5">
<h3>如何使用外部音频播放<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h3>
<p>1.控件属性from设置为1（外部文件）；2.path设置为路径（例：sd0/1.wav， 注：运行赋值需要加双引号）。</p>
</section>
</section>
<section id="id6">
<h2>音频控件-常见问题<a class="headerlink" href="#id6" title="此标题的永久链接"></a></h2>
<section id="wav0-vid-id">
<h3>wav0.vid 初始值无效:音频ID无效<a class="headerlink" href="#wav0-vid-id" title="此标题的永久链接"></a></h3>
<p>这是因为没有配置vid属性导致的，导入音频文件并配置vid属性即可</p>
<img alt="../_images/audio_10.jpg" src="../_images/audio_10.jpg" />
</section>
<section id="id7">
<h3>屏幕使用外部音频播放无声音<a class="headerlink" href="#id7" title="此标题的永久链接"></a></h3>
<p>1.屏幕是否识别到sd卡里的文件（可以用文件浏览器控件看是否有文件显示出来）；2.sd卡音频文件名是否和属性path设置的文件名是否一致（即path设置为sd0/1.wav，那么SD卡根目录音频文件名要为1.wav，否则无法识别到）；3.屏幕电路板是否有外接喇叭（sd卡旁边j5接口是否接有喇叭）。4.检测volume指令是否设置为0（默认为100）</p>
</section>
</section>
<section id="id8">
<h2>音频控件-样例工程下载<a class="headerlink" href="#id8" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/音频控件/音频控件使用.HMI">《音频控件使用》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/音频控件/数字播报.HMI">《数字播报》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/控件详解/音频控件/Telephone/电话.HMI">《电话》演示工程下载</a></p>
</section>
<section id="id12">
<h2>音频控件-相关链接<a class="headerlink" href="#id12" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../QA/QA10.html#id1"><span class="std std-ref">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</span></a></p>
<p><a class="reference internal" href="../QA/QA11.html#txtval"><span class="std std-ref">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</span></a></p>
<p><a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
<p><a class="reference internal" href="../variables/volume.html#volume"><span class="std std-ref">volume-系统音量</span></a></p>
<p><a class="reference internal" href="../QA/QA37.html#id2"><span class="std std-ref">觉得喇叭声音小怎么办</span></a></p>
<p><a class="reference internal" href="../QA/QA37.html#id4"><span class="std std-ref">喇叭接口型号</span></a></p>
<p><a class="reference internal" href="../QA/QA37.html#id5"><span class="std std-ref">喇叭的正负极</span></a></p>
<p><a class="reference internal" href="../QA/QA37.html#id6"><span class="std std-ref">喇叭插上有电流声</span></a></p>
</section>
<section id="id13">
<h2>音频控件-属性详解<a class="headerlink" href="#id13" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="component.html#id6"><span class="std std-ref">控件属性解析</span></a></p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>绿色属性可以通过上位机或者串口屏指令进行修改，黑色属性只能在上位机中修改或者不可修改，可通过上位机进行修改指“选中控件后通过属性栏修改控件的属性”</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">type属性</span></code> -控件类型，固定值，不同类型的控件type值不同，相同类型的控件type值相同，可读，不可通过上位机修改，不可通过指令修改。参考： <a class="reference internal" href="component.html#id10"><span class="std std-ref">控件属性-控件id对照表</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">id属性</span></code> -控件ID，可通过上位机左上角的上下箭头置顶或置底，可读，可通过上位机修改左上角的箭头置顶或置地间接修改，不可通过指令修改。参考： <a class="reference internal" href="../QA/QA28.html#id1"><span class="std std-ref">如何更改控件的前后图层关系</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">objname属性</span></code> -控件名称。不可读，可通过上位机进行修改，不可通过指令更改。</p>
<p><code class="docutils literal notranslate"><span class="pre">vscope属性</span></code> -内存占用(私有占用只能在当前页面被访问，全局占用可以在所有页面被访问)，当设置为私有时，跳转页面后，该控件占用的内存会被释放，重新返回该页面后该控件会恢复到最初的设置。可读，可通过上位机进行修改，不可通过指令更改。参考：<a class="reference internal" href="../grammar/global_variable.html#id1"><span class="std std-ref">跨页面赋值，全局变量操作</span></a></p>
<p><code class="docutils literal notranslate"><span class="pre">from属性</span></code> -播放源，内部资源文件或外部文件，可通过上位机进行修改，不可通过指令更改</p>
<p><code class="docutils literal notranslate"><span class="pre">vid属性</span></code> -音频ID。播放源为内部资源文件时显示。可通过上位机进行修改，可通过指令更改</p>
<p><code class="docutils literal notranslate"><span class="pre">path属性</span></code> -外部音频文件路径(如:”ram/0.wav”或”sd0/1.wav”)。播放源为外部文件时显示。可通过上位机进行修改，可通过指令更改。</p>
<p><code class="docutils literal notranslate"><span class="pre">en属性</span></code> -播放状态(0-停止;1-播放;2-暂停)，可通过上位机进行修改，可通过指令更改</p>
<p><code class="docutils literal notranslate"><span class="pre">loop属性</span></code> -循环播放:0-否;1-是，可通过上位机进行修改，可通过指令更改</p>
<p><code class="docutils literal notranslate"><span class="pre">tim属性</span></code> -当前播放时间(ms)，可通过上位机进行修改，可通过指令更改</p>
<p><code class="docutils literal notranslate"><span class="pre">stim属性</span></code> -总时间(由音频文件决定，不可更改，运行中可获取)</p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="Video.html" class="btn btn-neutral float-left" title="视频控件" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="ExPicture.html" class="btn btn-neutral float-right" title="外部图片控件" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>