<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>在键盘页面按下OK键时将参数通过串口发送出去 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/translations.js"></script>
        <script src="../../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../../_static/custom.js"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../../genindex.html" />
    <link rel="search" title="搜索" href="../../../search.html" />
    <link rel="next" title="键盘只能输入32个字符需要改成能输入更多的字符" href="keyboard_custom7.html" />
    <link rel="prev" title="键盘页面限制输入数据的值" href="keyboard_custom5.html" />
    <link href="../../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../../index.html">串口屏高级应用详解</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="../index.html">系统键盘</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="../keyboard_base.html">键盘基础知识</a></li>
<li class="toctree-l3"><a class="reference internal" href="../keyboard_principle.html">键盘的实现原理</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html">自定义键盘</a><ul class="current">
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom1.html">进入键盘时自动将输入框清空</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom2.html">键盘页面默认为拼音输入</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom3.html">键盘页面默认为英文大写</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom4.html">如何让键盘的光标不闪烁</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom5.html">键盘页面限制输入数据的值</a></li>
<li class="toctree-l4 current"><a class="current reference internal" href="#">在键盘页面按下OK键时将参数通过串口发送出去</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom7.html">键盘只能输入32个字符需要改成能输入更多的字符</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom8.html">不同控件在系统键盘设置好参数存储到用户存储区</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom9.html">键盘输入回车</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom10.html">进入键盘时显示不同的备注信息</a></li>
<li class="toctree-l4"><a class="reference internal" href="keyboard_custom11.html">自定义好看的键盘</a></li>
<li class="toctree-l4"><a class="reference internal" href="index.html#keyboardlink">键盘-相关链接</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../keyboard_problem.html">系统键盘常见问题</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../recmod/index.html">主动解析模式应用详解</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../hmi_download_protocol.html">HMI下载协议详解/OTA升级</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc.html">串口指令增加CRC校验</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc2.html">程序中使用CRC校验数据</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../transmit_data.html">运行中串口传输文件到内存或SD卡</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../SDcard.html">SD卡读写文件流程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../expandIO.html">拓展IO资料</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../get_property.html">获取控件自身的属性</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../wepo_repo.html">读写用户存储区</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getv.html">获取串口屏设备唯一序列号</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../../index.html">串口屏高级应用详解</a> &raquo;</li>
          <li><a href="../index.html">系统键盘</a> &raquo;</li>
          <li><a href="index.html">自定义键盘</a> &raquo;</li>
      <li>在键盘页面按下OK键时将参数通过串口发送出去</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="ok">
<span id="keyboardsend"></span><h1>在键盘页面按下OK键时将参数通过串口发送出去<a class="headerlink" href="#ok" title="此标题的永久链接"></a></h1>
<p>1、以文本类型发送</p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>编辑ok按钮的弹起事件,在page指令前添加以下代码</p>
</div>
<img alt="../../../_images/1_5.png" src="../../../_images/1_5.png" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>page指令之后的指令是不会被执行的，因为已经跳转到别的页面了。</p>
</div>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>这里作为帧头的 55 并不固定，是可以修改为你自己定义的其他帧头，你可以定义多个字节作为帧头</p>
<p>00 01 02 是作为标记来区分n0.val、n1.val、n2.val，可以根据自己的需求进行更改</p>
</div>
<p>完整的代码如下</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="c1">//调用此页之前，先对此页的loadpageid.val和loadcmpid.val赋值就可以了，其他的逻辑本页会自动实现</span>
<span class="linenos"> 2</span><span class="c1">//loadpageid.val表示调用页的页面ID,loadcmpid.val表示调用页的控件ID</span>
<span class="linenos"> 3</span><span class="k">if</span><span class="p">(</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">type</span><span class="o">==</span><span class="mi">54</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 4</span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 5</span><span class="w">   </span><span class="n">covx</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">val</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos"> 6</span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">type</span><span class="o">==</span><span class="mi">59</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 7</span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 8</span><span class="w">   </span><span class="n">covx</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos"> 9</span><span class="k">if</span><span class="p">(</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="mi">0</span><span class="p">)</span><span class="w"></span>
<span class="linenos">10</span><span class="p">{</span><span class="w"></span>
<span class="linenos">11</span><span class="w">   </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">*=</span><span class="mi">-1</span><span class="w"></span>
<span class="linenos">12</span><span class="p">}</span><span class="w"></span>
<span class="linenos">13</span><span class="k">for</span><span class="p">(</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="mi">0</span><span class="p">;</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">vvs1</span><span class="p">;</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="o">++</span><span class="p">)</span><span class="w"></span>
<span class="linenos">14</span><span class="p">{</span><span class="w"></span>
<span class="linenos">15</span><span class="w">   </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">*=</span><span class="mi">10</span><span class="w"></span>
<span class="linenos">16</span><span class="p">}</span><span class="w"></span>
<span class="linenos">17</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">val</span><span class="o">=</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">18</span><span class="n">strlen</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">19</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">--</span><span class="w"></span>
<span class="linenos">20</span><span class="k">while</span><span class="p">(</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">&gt;=</span><span class="mi">0</span><span class="p">)</span><span class="w"></span>
<span class="linenos">21</span><span class="p">{</span><span class="w"></span>
<span class="linenos">22</span><span class="w">   </span><span class="n">substr</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span><span class="w"></span>
<span class="linenos">23</span><span class="w">   </span><span class="k">if</span><span class="p">(</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="o">==</span><span class="s">&quot;.&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">24</span><span class="w">   </span><span class="p">{</span><span class="w"></span>
<span class="linenos">25</span><span class="w">      </span><span class="n">substr</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">+</span><span class="mi">1</span><span class="p">,</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">vvs1</span><span class="w"></span>
<span class="linenos">26</span><span class="w">      </span><span class="n">covx</span><span class="w"> </span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">27</span><span class="w">      </span><span class="n">strlen</span><span class="w"> </span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">28</span><span class="w">      </span><span class="k">while</span><span class="p">(</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">vvs1</span><span class="p">)</span><span class="w"></span>
<span class="linenos">29</span><span class="w">      </span><span class="p">{</span><span class="w"></span>
<span class="linenos">30</span><span class="w">         </span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="o">*=</span><span class="mi">10</span><span class="w"></span>
<span class="linenos">31</span><span class="w">         </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">++</span><span class="w"></span>
<span class="linenos">32</span><span class="w">      </span><span class="p">}</span><span class="w"></span>
<span class="linenos">33</span><span class="w">      </span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">val</span><span class="o">+=</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">34</span><span class="w">      </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="mi">-1</span><span class="w"></span>
<span class="linenos">35</span><span class="w">   </span><span class="p">}</span><span class="w"></span>
<span class="linenos">36</span><span class="w">   </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">--</span><span class="w"></span>
<span class="linenos">37</span><span class="p">}</span><span class="w"></span>
<span class="linenos">38</span><span class="n">substr</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">1</span><span class="w"></span>
<span class="linenos">39</span><span class="k">if</span><span class="p">(</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="o">==</span><span class="s">&quot;-&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">40</span><span class="p">{</span><span class="w"></span>
<span class="linenos">41</span><span class="w">   </span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">val</span><span class="o">*=</span><span class="mi">-1</span><span class="w"></span>
<span class="linenos">42</span><span class="p">}</span><span class="w"></span>
<span class="linenos">43</span><span class="p">}</span><span class="k">else</span><span class="w"></span>
<span class="linenos">44</span><span class="p">{</span><span class="w"></span>
<span class="linenos">45</span><span class="w">   </span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">txt</span><span class="o">=</span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="w"></span>
<span class="linenos">46</span><span class="p">}</span><span class="w"></span>
<span class="linenos">47</span><span class="c1">//区分不同页面的不同变量</span>
<span class="linenos">48</span><span class="k">if</span><span class="p">(</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">0</span><span class="o">&amp;&amp;</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="p">.</span><span class="n">n0</span><span class="p">.</span><span class="n">id</span><span class="p">)</span><span class="w"></span>
<span class="linenos">49</span><span class="p">{</span><span class="w"></span>
<span class="linenos">50</span><span class="w">   </span><span class="n">printh</span><span class="w"> </span><span class="mi">55</span><span class="w"> </span><span class="mo">00</span><span class="w">  </span><span class="c1">//先发送一个帧头，用来告诉下位机这个是n0</span>
<span class="linenos">51</span><span class="w">   </span><span class="n">prints</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="w">  </span><span class="c1">//发送输入框内的数据</span>
<span class="linenos">52</span><span class="w">   </span><span class="n">printh</span><span class="w"> </span><span class="mi">0</span><span class="n">d</span><span class="w"> </span><span class="mi">0</span><span class="n">a</span><span class="w">  </span><span class="c1">//发送帧尾</span>
<span class="linenos">53</span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">0</span><span class="o">&amp;&amp;</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="p">.</span><span class="n">n1</span><span class="p">.</span><span class="n">id</span><span class="p">)</span><span class="w"></span>
<span class="linenos">54</span><span class="p">{</span><span class="w"></span>
<span class="linenos">55</span><span class="w">   </span><span class="n">printh</span><span class="w"> </span><span class="mi">55</span><span class="w"> </span><span class="mo">01</span><span class="w">  </span><span class="c1">//先发送一个帧头，用来告诉下位机这个是n1</span>
<span class="linenos">56</span><span class="w">   </span><span class="n">prints</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="w">  </span><span class="c1">//发送输入框内的数据</span>
<span class="linenos">57</span><span class="w">   </span><span class="n">printh</span><span class="w"> </span><span class="mi">0</span><span class="n">d</span><span class="w"> </span><span class="mi">0</span><span class="n">a</span><span class="w">  </span><span class="c1">//发送帧尾</span>
<span class="linenos">58</span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">0</span><span class="o">&amp;&amp;</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="p">.</span><span class="n">n2</span><span class="p">.</span><span class="n">id</span><span class="p">)</span><span class="w"></span>
<span class="linenos">59</span><span class="p">{</span><span class="w"></span>
<span class="linenos">60</span><span class="w">   </span><span class="n">printh</span><span class="w"> </span><span class="mi">55</span><span class="w"> </span><span class="mo">02</span><span class="w">  </span><span class="c1">//先发送一个帧头，用来告诉下位机这个是n2</span>
<span class="linenos">61</span><span class="w">   </span><span class="n">prints</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="w">  </span><span class="c1">//发送输入框内的数据</span>
<span class="linenos">62</span><span class="w">   </span><span class="n">printh</span><span class="w"> </span><span class="mi">0</span><span class="n">d</span><span class="w"> </span><span class="mi">0</span><span class="n">a</span><span class="w">  </span><span class="c1">//发送帧尾</span>
<span class="linenos">63</span><span class="p">}</span><span class="w"></span>
<span class="linenos">64</span><span class="n">page</span><span class="w"> </span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
</pre></div>
</div>
<p>2、以数值类型发送</p>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>编辑ok按钮的弹起事件,在page指令前添加以下代码</p>
</div>
<img alt="../../../_images/1_6.png" src="../../../_images/1_6.png" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>page指令之后的指令是不会被执行的，因为已经跳转到别的页面了。</p>
</div>
<div class="admonition hint">
<p class="admonition-title">提示</p>
<p>这里作为帧头的 55 并不固定，是可以修改为你自己定义的其他帧头，你可以定义多个字节作为帧头</p>
<p>00 01 02 是作为标记来区分n0.val、n1.val、n2.val，可以根据自己的需求进行更改</p>
</div>
<p>完整的代码如下</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="w"> </span><span class="c1">//调用此页之前，先对此页的loadpageid.val和loadcmpid.val赋值就可以了，其他的逻辑本页会自动实现</span>
<span class="linenos"> 2</span><span class="w"> </span><span class="c1">//loadpageid.val表示调用页的页面ID,loadcmpid.val表示调用页的控件ID</span>
<span class="linenos"> 3</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">type</span><span class="o">==</span><span class="mi">54</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 4</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 5</span><span class="w">     </span><span class="n">covx</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">val</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos"> 6</span><span class="w"> </span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">type</span><span class="o">==</span><span class="mi">59</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 7</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 8</span><span class="w">     </span><span class="n">covx</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos"> 9</span><span class="w">     </span><span class="k">if</span><span class="p">(</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="mi">0</span><span class="p">)</span><span class="w"></span>
<span class="linenos">10</span><span class="w">     </span><span class="p">{</span><span class="w"></span>
<span class="linenos">11</span><span class="w">     </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">*=</span><span class="mi">-1</span><span class="w"></span>
<span class="linenos">12</span><span class="w">     </span><span class="p">}</span><span class="w"></span>
<span class="linenos">13</span><span class="w">     </span><span class="k">for</span><span class="p">(</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="mi">0</span><span class="p">;</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">vvs1</span><span class="p">;</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="o">++</span><span class="p">)</span><span class="w"></span>
<span class="linenos">14</span><span class="w">     </span><span class="p">{</span><span class="w"></span>
<span class="linenos">15</span><span class="w">     </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">*=</span><span class="mi">10</span><span class="w"></span>
<span class="linenos">16</span><span class="w">     </span><span class="p">}</span><span class="w"></span>
<span class="linenos">17</span><span class="w">     </span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">val</span><span class="o">=</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">18</span><span class="w">     </span><span class="n">strlen</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">19</span><span class="w">     </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">--</span><span class="w"></span>
<span class="linenos">20</span><span class="w">     </span><span class="k">while</span><span class="p">(</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">&gt;=</span><span class="mi">0</span><span class="p">)</span><span class="w"></span>
<span class="linenos">21</span><span class="w">     </span><span class="p">{</span><span class="w"></span>
<span class="linenos">22</span><span class="w">     </span><span class="n">substr</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">1</span><span class="w"></span>
<span class="linenos">23</span><span class="w">     </span><span class="k">if</span><span class="p">(</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="o">==</span><span class="s">&quot;.&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">24</span><span class="w">     </span><span class="p">{</span><span class="w"></span>
<span class="linenos">25</span><span class="w">         </span><span class="n">substr</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">+</span><span class="mi">1</span><span class="p">,</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">vvs1</span><span class="w"></span>
<span class="linenos">26</span><span class="w">         </span><span class="n">covx</span><span class="w"> </span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">27</span><span class="w">         </span><span class="n">strlen</span><span class="w"> </span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">28</span><span class="w">         </span><span class="k">while</span><span class="p">(</span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">&lt;</span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">vvs1</span><span class="p">)</span><span class="w"></span>
<span class="linenos">29</span><span class="w">         </span><span class="p">{</span><span class="w"></span>
<span class="linenos">30</span><span class="w">         </span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="o">*=</span><span class="mi">10</span><span class="w"></span>
<span class="linenos">31</span><span class="w">         </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">++</span><span class="w"></span>
<span class="linenos">32</span><span class="w">         </span><span class="p">}</span><span class="w"></span>
<span class="linenos">33</span><span class="w">         </span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">val</span><span class="o">+=</span><span class="n">temp2</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">34</span><span class="w">         </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="mi">-1</span><span class="w"></span>
<span class="linenos">35</span><span class="w">     </span><span class="p">}</span><span class="w"></span>
<span class="linenos">36</span><span class="w">     </span><span class="n">temp</span><span class="p">.</span><span class="n">val</span><span class="o">--</span><span class="w"></span>
<span class="linenos">37</span><span class="w">     </span><span class="p">}</span><span class="w"></span>
<span class="linenos">38</span><span class="w">     </span><span class="n">substr</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">1</span><span class="w"></span>
<span class="linenos">39</span><span class="w">     </span><span class="k">if</span><span class="p">(</span><span class="n">tempstr</span><span class="p">.</span><span class="n">txt</span><span class="o">==</span><span class="s">&quot;-&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">40</span><span class="w">     </span><span class="p">{</span><span class="w"></span>
<span class="linenos">41</span><span class="w">     </span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">val</span><span class="o">*=</span><span class="mi">-1</span><span class="w"></span>
<span class="linenos">42</span><span class="w">     </span><span class="p">}</span><span class="w"></span>
<span class="linenos">43</span><span class="w"> </span><span class="p">}</span><span class="k">else</span><span class="w"></span>
<span class="linenos">44</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">45</span><span class="w">     </span><span class="n">p</span><span class="p">[</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">b</span><span class="p">[</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="p">].</span><span class="n">txt</span><span class="o">=</span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="w"></span>
<span class="linenos">46</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
<span class="linenos">47</span><span class="w"> </span><span class="c1">//区分不同页面的不同变量</span>
<span class="linenos">48</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">0</span><span class="o">&amp;&amp;</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="p">.</span><span class="n">n0</span><span class="p">.</span><span class="n">id</span><span class="p">)</span><span class="w"></span>
<span class="linenos">49</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">50</span><span class="w">     </span><span class="c1">//先发送一个帧头，用来告诉下位机这个是n0</span>
<span class="linenos">51</span><span class="w">     </span><span class="n">printh</span><span class="w"> </span><span class="mi">55</span><span class="w"> </span><span class="mo">00</span><span class="w"></span>
<span class="linenos">52</span>
<span class="linenos">53</span><span class="w">     </span><span class="c1">//将文本转换为数值</span>
<span class="linenos">54</span><span class="w">     </span><span class="n">covx</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">sys0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">55</span>
<span class="linenos">56</span><span class="w">     </span><span class="c1">//发送输入框内的数据</span>
<span class="linenos">57</span><span class="w">     </span><span class="n">prints</span><span class="w"> </span><span class="n">sys0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">58</span>
<span class="linenos">59</span><span class="w">     </span><span class="c1">//发送帧尾</span>
<span class="linenos">60</span><span class="w">     </span><span class="n">printh</span><span class="w"> </span><span class="mi">0</span><span class="n">d</span><span class="w"> </span><span class="mi">0</span><span class="n">a</span><span class="w"></span>
<span class="linenos">61</span>
<span class="linenos">62</span><span class="w"> </span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">0</span><span class="o">&amp;&amp;</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="p">.</span><span class="n">n1</span><span class="p">.</span><span class="n">id</span><span class="p">)</span><span class="w"></span>
<span class="linenos">63</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">64</span><span class="w">     </span><span class="c1">//先发送一个帧头，用来告诉下位机这个是n1</span>
<span class="linenos">65</span><span class="w">     </span><span class="n">printh</span><span class="w"> </span><span class="mi">55</span><span class="w"> </span><span class="mo">01</span><span class="w"></span>
<span class="linenos">66</span>
<span class="linenos">67</span><span class="w">     </span><span class="c1">//将文本转换为数值</span>
<span class="linenos">68</span><span class="w">     </span><span class="n">covx</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">sys0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">69</span>
<span class="linenos">70</span><span class="w">     </span><span class="c1">//发送输入框内的数据</span>
<span class="linenos">71</span><span class="w">     </span><span class="n">prints</span><span class="w"> </span><span class="n">sys0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">72</span>
<span class="linenos">73</span><span class="w">     </span><span class="c1">//发送帧尾</span>
<span class="linenos">74</span><span class="w">     </span><span class="n">printh</span><span class="w"> </span><span class="mi">0</span><span class="n">d</span><span class="w"> </span><span class="mi">0</span><span class="n">a</span><span class="w"></span>
<span class="linenos">75</span><span class="w"> </span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="p">(</span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="mi">0</span><span class="o">&amp;&amp;</span><span class="n">loadcmpid</span><span class="p">.</span><span class="n">val</span><span class="o">==</span><span class="n">page0</span><span class="p">.</span><span class="n">n2</span><span class="p">.</span><span class="n">id</span><span class="p">)</span><span class="w"></span>
<span class="linenos">76</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">77</span><span class="w">     </span><span class="c1">//先发送一个帧头，用来告诉下位机这个是n2</span>
<span class="linenos">78</span><span class="w">     </span><span class="n">printh</span><span class="w"> </span><span class="mi">55</span><span class="w"> </span><span class="mo">02</span><span class="w"></span>
<span class="linenos">79</span>
<span class="linenos">80</span><span class="w">     </span><span class="c1">//将文本转换为数值</span>
<span class="linenos">81</span><span class="w">     </span><span class="n">covx</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">sys0</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">82</span>
<span class="linenos">83</span><span class="w">     </span><span class="c1">//发送输入框内的数据</span>
<span class="linenos">84</span><span class="w">     </span><span class="n">prints</span><span class="w"> </span><span class="n">sys0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
<span class="linenos">85</span>
<span class="linenos">86</span><span class="w">     </span><span class="c1">//发送帧尾</span>
<span class="linenos">87</span><span class="w">     </span><span class="n">printh</span><span class="w"> </span><span class="mi">0</span><span class="n">d</span><span class="w"> </span><span class="mi">0</span><span class="n">a</span><span class="w"></span>
<span class="linenos">88</span><span class="w"> </span><span class="p">}</span><span class="w"></span>
<span class="linenos">89</span><span class="w"> </span><span class="n">page</span><span class="w"> </span><span class="n">loadpageid</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
</pre></div>
</div>
<section id="id1">
<h2>在键盘页面按下OK键时将参数通过串口发送出去-样例工程下载<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/系统键盘/在键盘页面按下OK键时将参数通过串口发送出去-以数值类型发送.HMI">《在键盘页面按下OK键时将参数通过串口发送出去-以数值类型发送》演示工程下载</a></p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/系统键盘/在键盘页面按下OK键时将参数通过串口发送出去-以文本类型发送.HMI">《在键盘页面按下OK键时将参数通过串口发送出去-以文本类型发送》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="keyboard_custom5.html" class="btn btn-neutral float-left" title="键盘页面限制输入数据的值" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="keyboard_custom7.html" class="btn btn-neutral float-right" title="键盘只能输入32个字符需要改成能输入更多的字符" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>