<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>字符编码相关详解——串口怎么发送字符和汉字 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="为什么转换后的图片/视频/音频体积变大了" href="QA122.html" />
    <link rel="prev" title="串口屏连接单片机开发板注意事项" href="QA120.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">常见问题</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#id2">硬件相关</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">软件相关</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id4">错误提示</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id5">其他</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="QA46.html">如何查看编译时输出的信息</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA48.html">不同页面之间的同名控件有什么联系</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA51.html">在定时器0里面加入使能定时器1的语句为什么系统不执行</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA52.html">大小端模式</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA53.html">如何删除控件</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA57.html">使用串口下载工程速度慢怎么办</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA58.html">printf()重定向之后，发送命令和结束符</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA59.html">实现自己的printf函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA61.html">单片机如何控制屏幕</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA64.html">虚拟sd卡文件夹如何打开</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA65.html">如何实现页面滑动切换</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA66.html">如何设置页面背景</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA67.html">HMI文件和TFT文件的关系</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA69.html">界面旋转/翻转/方向</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA73.html">串口屏如何制作弹窗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA76.html">utf8字库下如何只选择汉字</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA77.html">TFT文件如何下载到串口屏中</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA78.html">只有屏/tft文件,或者源HMI工程丢了,能反编译出HMI文件吗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA80.html">如何导入导出page文件</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA82.html">屏幕保护制作</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA85.html">二维码控件是gb2312还是utf8编码</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA86.html">修改工程编码为utf8或者GB2312</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA87.html">文本数据显示不完全</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA92.html">键盘无法输入中文</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA93.html">单片机如何判断串口屏有没有接入</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA94.html">实现按键锁定功能</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA96.html">串口屏的串口缓冲区大小</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA97.html">主动解析下如何提取负数</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA100.html">带外壳产品安装示意图</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA101.html">显示比例不对，正方形变成长方形，圆型变成椭圆</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA103.html">发送中文汉字相关问题</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA105.html">为什么关闭小板上的电源时，RX灯会亮起</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA106.html">变量控件名和页面名称可以一样吗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA107.html">如何使用TFT文件下载助手(TFTFileDownload)下载工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA110.html">单片机3.3V的串口可以直接接串口屏的串口吗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA114.html">如何安装新字体</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA115.html">如何导入字库</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA116.html">如何导入图片</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA117.html">如何导入动画</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA118.html">如何导入视频</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA119.html">如何导入音频</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA120.html">串口屏连接单片机开发板注意事项</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">字符编码相关详解——串口怎么发送字符和汉字</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#id2">为什么要使用字符编码</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="QA122.html">为什么转换后的图片/视频/音频体积变大了</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA124.html">如何测量屏幕的尺寸大小</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA130.html">MAC电脑如何开发淘晶驰串口屏</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA131.html">数据记录文件结构分析</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA132.html">哪些指令和操作会消耗flash寿命</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">常见问题</a> &raquo;</li>
      <li>字符编码相关详解——串口怎么发送字符和汉字</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>字符编码相关详解——串口怎么发送字符和汉字<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<p>先说一个结论:你可以直接将编码理解为文字(包括字母/符号等)在字符编码表中的对应顺序</p>
<section id="id2">
<h2>为什么要使用字符编码<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>你有没有想过，虽然你通过单片机的串口发送了“1234”，“abcd”等字符串出来,但是明明单片机的引脚只能传输和接收高低电平,但是你却能看到各种字符串,这到底是什么原因呢</p>
<p>以下举个简单的例子:</p>
<p>在考试时，你和你的同桌想传递选择题的答案，但是你们只有一个能显示数字的计算器</p>
<img alt="../_images/QA121_0.jpg" src="../_images/QA121_0.jpg" />
<p>但是你直接输入了 1 2 3 4 ，然后把计算器偷偷递给你的同桌，你的同桌接过来一看，原来答案是 A B C D</p>
<p>这个你和同桌临时建立起来的选择题答案编码，其实就是一个数字和字符的映射表，1表示A，2表示B，3表示C，4表示D</p>
<p>ASCII编码其实也是同样的原理，</p>
<p>在ASCII编码中，字符“0”用十进制的48表示，字符“1”用十进制的49表示，以此类推，字符“9”用十进制的57表示。</p>
<p>那为什么是从48开始呢，因为48的十六进制是0x30，57的十六进制是0x39</p>
<p>因此字符‘0’对应了0x30，字符‘9’对应了0x39</p>
<p>大写字母A-Z分别对应了0x41到0x5A。</p>
<p>小写字母a-z分别对应了0x61到0x7A。</p>
<p>其他零散的位置分别用于存放符号（例如”！&#64;#￥%……&amp;,./&lt;&gt;?”以及空格等），以及一些控制命令（例如”回车”，”换行”等）</p>
<img alt="../_images/QA121_1.jpg" src="../_images/QA121_1.jpg" />
<p>现在我们知道了ASCII编码和ASCII字符的对应关系，但是明明单片机的引脚只能传输和接收高低电平，是怎么将数值传送出去的呢？</p>
<p>接下来再举个例子</p>
<p>这是个语文/英语考试，你找不到带计算器的借口,于是你和同桌约定好了用眨眼睛来传输数据，闭眼代表0（低电平），睁眼代表1（高电平），并且你的同桌一秒钟观察你一次，记录你的眼睛状态</p>
<p>你们约定好以ASCII编码来传输字符</p>
<p>要传输的数据是 A B C D ，对应的ASCII编码是  0x41  0x42 0x43  0x44</p>
<p>接下来需要将十六进制转换为二进制（这一转换是有技巧的，很简单，请自行百度以下）</p>
<p>0x41对应的二进制是 0100 0001 ，于是你开始眨眼， 闭睁闭闭 闭闭闭睁，相当于单片机发出了电平 低高低低 低低低高</p>
<p>0x42对应的二进制是 0100 0010 ，于是你开始眨眼， 闭睁闭闭 闭闭睁闭，相当于单片机发出了电平 低高低低 低低高低</p>
<p>0x43对应的二进制是 0100 0011 ，于是你开始眨眼， 闭睁闭闭 闭闭睁睁，相当于单片机发出了电平 低高低低 低低高高</p>
<p>0x44对应的二进制是 0100 0100 ，于是你开始眨眼， 闭睁闭闭 闭睁闭闭，相当于单片机发出了电平 低高低低 低高低低</p>
<p>原理讲解完毕，如果你已经理解串口是怎么传输数字和字母，那你能说出串口是怎么传输汉字的吗?</p>
<p>汉字传输的方法和ASCII编码类似，也是有一个编码对应着汉字，但是目前主流的汉字编码有两种，分别是GB2312以及UTF8编码，在传输汉字时，需要保证两边的编码是一致的，即两边都是GB2312或者两边都是UTF8</p>
<p>在GB2312编码下</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;淘晶驰&quot;</span><span class="w"></span>
<span class="linenos">2</span><span class="w"> </span><span class="n">prints</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<p>GB2312编码下发出的数据为 CC D4 BE A7 B3 DB ，GB2312编码下，每个汉字占用2个字节，淘晶驰共三个字，共发出6个字节</p>
<p>在UTF-8编码下</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="o">=</span><span class="s">&quot;淘晶驰&quot;</span><span class="w"></span>
<span class="linenos">2</span><span class="w"> </span><span class="n">prints</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<p>UTF-8编码下发出的数据为 E6 B7 98 E6 99 B6 E9 A9 B0 ，UTF-8编码下，每个汉字占用3个字节，淘晶驰共三个字，共发出6个字节</p>
<p>相关链接: <a class="reference internal" href="../debug/base/tjc2mcu.html#id1"><span class="std std-ref">串口屏与单片机连接</span></a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="QA120.html" class="btn btn-neutral float-left" title="串口屏连接单片机开发板注意事项" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="QA122.html" class="btn btn-neutral float-right" title="为什么转换后的图片/视频/音频体积变大了" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>