<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>解析不定长字符串(以回车换行结尾) &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../../../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
        <script src="../../../_static/jquery.js"></script>
        <script src="../../../_static/underscore.js"></script>
        <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../../../_static/doctools.js"></script>
        <script src="../../../_static/translations.js"></script>
        <script src="../../../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../../../_static/custom.js"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../../../genindex.html" />
    <link rel="search" title="搜索" href="../../../search.html" />
    <link rel="next" title="HMI下载协议详解/OTA升级" href="../../hmi_download_protocol.html" />
    <link rel="prev" title="接收json数据字符串" href="recmod_ascii_json.html" />
    <link href="../../../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../../../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../../../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="../../index.html">串口屏高级应用详解</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../../keyboard/index.html">系统键盘</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="../index.html">主动解析模式应用详解</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="../recmod_base.html">主动解析基本知识</a></li>
<li class="toctree-l3"><a class="reference internal" href="../fixed_hex/index.html">解析定长hex格式指令-自定义协议</a></li>
<li class="toctree-l3"><a class="reference internal" href="../unfixed_hex/index.html">解析不定长hex格式指令-自定义协议</a></li>
<li class="toctree-l3 current"><a class="reference internal" href="index.html">解析字符串格式指令</a><ul class="current">
<li class="toctree-l4"><a class="reference internal" href="recmod_ascii_attention.html">解析AT指令</a></li>
<li class="toctree-l4"><a class="reference internal" href="recmod_ascii_json.html">接收json数据字符串</a></li>
<li class="toctree-l4 current"><a class="current reference internal" href="#">解析不定长字符串(以回车换行结尾)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../hmi_download_protocol.html">HMI下载协议详解/OTA升级</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc.html">串口指令增加CRC校验</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../crc2.html">程序中使用CRC校验数据</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../transmit_data.html">运行中串口传输文件到内存或SD卡</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../SDcard.html">SD卡读写文件流程</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../expandIO.html">拓展IO资料</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../get_property.html">获取控件自身的属性</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../wepo_repo.html">读写用户存储区</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../getv.html">获取串口屏设备唯一序列号</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="../../index.html">串口屏高级应用详解</a> &raquo;</li>
          <li><a href="../index.html">主动解析模式应用详解</a> &raquo;</li>
          <li><a href="index.html">解析字符串格式指令</a> &raquo;</li>
      <li>解析不定长字符串(以回车换行结尾)</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>解析不定长字符串(以回车换行结尾)<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>每个页面仅建议在一个定时器中读取串口缓冲区的数据,在多个定时器中读取串口缓冲区的数据容易照成逻辑混乱。</p>
</div>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>字符串需要以 0d 0a 结尾</p>
</div>
<p>新建一个空白工程</p>
<p>在工程中新建一个定时器tmDecode，tim设置为50，en设置为1，用于定时解析串口数据</p>
<p>新建数字控件t0，txt_maxl设置为200，用于显示解析出来的数据</p>
<p>program.s中的配置如图所示</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="o">//</span><span class="n">以下代码只在上电时运行一次</span><span class="p">,</span><span class="n">一般用于全局变量定义和上电初始化数据</span>
<span class="linenos"> 2</span><span class="o">//</span><span class="n">全局变量定义目前仅支持4字节有符号整形</span><span class="p">(</span><span class="nb">int</span><span class="p">),</span><span class="n">不支持其他类型的全局变量声明</span><span class="p">,</span><span class="n">如需使用字符串类型可以在页面中使用变量控件来实现</span>
<span class="linenos"> 3</span><span class="nb">int</span> <span class="n">sys0</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span><span class="n">sys1</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span><span class="n">sys2</span><span class="o">=</span><span class="mi">0</span>
<span class="linenos"> 4</span><span class="nb">int</span> <span class="n">usize2</span><span class="p">,</span><span class="n">length</span><span class="p">,</span><span class="n">totalLength</span>
<span class="linenos"> 5</span><span class="o">//</span><span class="n">波特率115200</span>
<span class="linenos"> 6</span><span class="n">bauds</span><span class="o">=</span><span class="mi">115200</span>
<span class="linenos"> 7</span><span class="o">//</span><span class="n">亮度100</span>
<span class="linenos"> 8</span><span class="n">dim</span><span class="o">=</span><span class="mi">100</span>
<span class="linenos"> 9</span><span class="o">//</span><span class="n">打开主动解析</span>
<span class="linenos">10</span><span class="n">recmod</span><span class="o">=</span><span class="mi">1</span>
<span class="linenos">11</span><span class="n">page</span> <span class="mi">0</span>                       <span class="o">//</span><span class="n">上电刷新第0页</span>
</pre></div>
</div>
<p>解析定时器（tim为50）中的代码如下图所示</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="k">if</span><span class="p">(</span><span class="n">usize</span><span class="o">&gt;=</span><span class="mi">2</span><span class="p">)</span>
<span class="linenos"> 2</span><span class="p">{</span>
<span class="linenos"> 3</span>  <span class="n">usize2</span><span class="o">=</span><span class="n">usize</span><span class="o">-</span><span class="mi">1</span>
<span class="linenos"> 4</span>  <span class="k">for</span><span class="p">(</span><span class="n">length</span><span class="o">=</span><span class="mi">0</span><span class="p">;</span><span class="n">length</span><span class="o">&lt;</span><span class="n">usize2</span><span class="p">;</span><span class="n">length</span><span class="o">++</span><span class="p">)</span>
<span class="linenos"> 5</span>  <span class="p">{</span>
<span class="linenos"> 6</span>    <span class="k">if</span><span class="p">(</span><span class="n">u</span><span class="p">[</span><span class="n">length</span><span class="p">]</span><span class="o">==</span><span class="mh">0x0d</span><span class="o">&amp;&amp;</span><span class="n">u</span><span class="p">[</span><span class="n">length</span><span class="o">+</span><span class="mi">1</span><span class="p">]</span><span class="o">==</span><span class="mh">0x0a</span><span class="p">)</span>
<span class="linenos"> 7</span>    <span class="p">{</span>
<span class="linenos"> 8</span>      <span class="n">totalLength</span><span class="o">=</span><span class="n">length</span><span class="o">+</span><span class="mi">2</span>
<span class="linenos"> 9</span>      <span class="n">ucopy</span> <span class="n">t0</span><span class="o">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="n">length</span><span class="p">,</span><span class="mi">0</span>
<span class="linenos">10</span>      <span class="n">udelete</span> <span class="n">totalLength</span>
<span class="linenos">11</span>    <span class="p">}</span>
<span class="linenos">12</span>  <span class="p">}</span>
<span class="linenos">13</span><span class="p">}</span>
</pre></div>
</div>
<section id="id2">
<h2>解析不定长字符串(以回车换行结尾)-样例工程下载<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/串口屏高级应用/主动解析/解析不定长字符串以回车换行结尾.HMI">《解析不定长字符串以回车换行结尾》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="recmod_ascii_json.html" class="btn btn-neutral float-left" title="接收json数据字符串" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="../../hmi_download_protocol.html" class="btn btn-neutral float-right" title="HMI下载协议详解/OTA升级" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>