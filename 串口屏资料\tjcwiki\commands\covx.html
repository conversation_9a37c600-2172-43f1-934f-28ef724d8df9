<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>covx-变量类型转换 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="strlen-字符串变量字符长度测试" href="strlen.html" />
    <link rel="prev" title="randset-随机数范围设置" href="randset.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">基本指令集</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id2">常用指令集</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="page.html">page-页面跳转指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="prints.html">prints-从串口打印一个变量/常量</a></li>
<li class="toctree-l3"><a class="reference internal" href="printh.html">printh-从串口打印16进制</a></li>
<li class="toctree-l3"><a class="reference internal" href="click.html">click-激活控件的按下/弹起事件</a></li>
<li class="toctree-l3"><a class="reference internal" href="vis.html">vis-隐藏/显示控件</a></li>
<li class="toctree-l3"><a class="reference internal" href="tsw.html">tsw-控件触摸使能</a></li>
<li class="toctree-l3"><a class="reference internal" href="randset.html">randset-随机数范围设置</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">covx-变量类型转换</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#covx-1">covx-示例1</a></li>
<li class="toctree-l4"><a class="reference internal" href="#covx-2">covx-示例2</a></li>
<li class="toctree-l4"><a class="reference internal" href="#covx-3">covx-示例3</a></li>
<li class="toctree-l4"><a class="reference internal" href="#covx-4">covx-示例4</a></li>
<li class="toctree-l4"><a class="reference internal" href="#covx-5">covx-示例5</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id1">covx指令-相关链接</a></li>
<li class="toctree-l4"><a class="reference internal" href="#id2">covx指令-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="strlen.html">strlen-字符串变量字符长度测试</a></li>
<li class="toctree-l3"><a class="reference internal" href="btlen.html">btlen-字符串变量字节长度测试</a></li>
<li class="toctree-l3"><a class="reference internal" href="substr.html">substr-字符串截取</a></li>
<li class="toctree-l3"><a class="reference internal" href="spstr.html">spstr-字符串分割</a></li>
<li class="toctree-l3"><a class="reference internal" href="touch_j.html">touch_j-触摸校准</a></li>
<li class="toctree-l3"><a class="reference internal" href="add.html">add-往曲线控件添加数据</a></li>
<li class="toctree-l3"><a class="reference internal" href="addt.html">addt-曲线数据透传指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="cle.html">cle-清除曲线控件中的数据</a></li>
<li class="toctree-l3"><a class="reference internal" href="rest.html">rest-复位串口屏</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">高级指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id4">文件操作指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id5">不常用指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#crc">CRC校验指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#gui">GUI绘图指令</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#hmi">HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">指令集</a> &raquo;</li>
      <li>covx-变量类型转换</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="covx">
<h1>covx-变量类型转换<a class="headerlink" href="#covx" title="此标题的永久链接"></a></h1>
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>串口屏上仅有两种数据类型，即数值和字符串类型，当需要将字符串类型转换为数字类型或者将数字类型转换为字符串类型时，才使用covx指令</p>
<p>目前仅有txt、path、dir、filter这4个属性属性是字符串型属性，其他属性一般都是数值型属性。</p>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>covx att1,att2,lenth,format

att1:源变量

att2:目标变量

lenth:字符串的长度(0为自动长度，非0为固定长度)

format:申明数值类型(0-数字;1-货币;2-Hex)
</pre></div>
</div>
<section id="covx-1">
<h2>covx-示例1<a class="headerlink" href="#covx-1" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//把滑块h0的val数值变量转换成10进制数字字符串并赋值给文本t0的txt变量,长度为自动</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">covx</span><span class="w"> </span><span class="n">h0</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<p>在滑块控件的滑动事件和弹起事件中都要写</p>
<img alt="../_images/covx_1.jpg" src="../_images/covx_1.jpg" />
<img alt="../_images/covx_2.jpg" src="../_images/covx_2.jpg" />
</section>
<section id="covx-2">
<h2>covx-示例2<a class="headerlink" href="#covx-2" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//把文本t0的txt十进制数字字符串变量转换为数值并赋值给滑块h0的val数值变量,长度为自动</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">covx</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">h0</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/covx_3.jpg" src="../_images/covx_3.jpg" />
</section>
<section id="covx-3">
<h2>covx-示例3<a class="headerlink" href="#covx-3" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//把文本t0.txt转为数字n0.val</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">covx</span><span class="w"> </span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/covx_4.jpg" src="../_images/covx_4.jpg" />
</section>
<section id="covx-4">
<h2>covx-示例4<a class="headerlink" href="#covx-4" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//把数字n0.val转为文本t0.txt</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">covx</span><span class="w"> </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="p">,</span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/covx_5.jpg" src="../_images/covx_5.jpg" />
</section>
<section id="covx-5">
<h2>covx-示例5<a class="headerlink" href="#covx-5" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//把main页面图片控件p0的pic数值变量转换成10进制数字字符串并赋值给main页面文本控件t0的txt变量,长度为自动</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">covx</span><span class="w"> </span><span class="n">main</span><span class="p">.</span><span class="n">p0</span><span class="p">.</span><span class="n">pic</span><span class="p">,</span><span class="n">main</span><span class="p">.</span><span class="n">t0</span><span class="p">.</span><span class="n">txt</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/covx_6.jpg" src="../_images/covx_6.jpg" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>lenth始终表示的是字符串长度，数值转字符串的时候是目标变量的长度，字符串转数值的时候是源变量长度。</p>
<p>当原变量是数值属性时，目标变量必须是字符串属性，当原变量是字符串属性时，目标变量必须是数值属性。</p>
<p>如果目标变量和源变量类型相同，转换失败。</p>
</div>
</section>
<section id="id1">
<h2>covx指令-相关链接<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../widgets/Number.html#id1"><span class="std std-ref">数字控件</span></a></p>
<p><a class="reference internal" href="../widgets/Text.html#id1"><span class="std std-ref">文本控件</span></a></p>
<p><a class="reference internal" href="../QA/QA11.html#txtval"><span class="std std-ref">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</span></a></p>
</section>
<section id="id2">
<h2>covx指令-样例工程下载<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/基本指令集/covx指令/covx指令.HMI">《covx指令》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="randset.html" class="btn btn-neutral float-left" title="randset-随机数范围设置" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="strlen.html" class="btn btn-neutral float-right" title="strlen-字符串变量字符长度测试" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>