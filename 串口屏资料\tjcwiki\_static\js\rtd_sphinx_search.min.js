"use strict";var MAX_SUGGESTIONS=50,MAX_SECTION_RESULTS=3,MAX_SUBSTRING_LIMIT=100,ANIMATION_TIME=200,FETCH_RESULTS_DELAY=250,CLEAR_RESULTS_DELAY=300,debounce=function(r,n){function e(){var e=this,t=arguments;clearTimeout(a),a=setTimeout(function(){return r.apply(e,t)},n)}var a;return e.cancel=function(){clearTimeout(a),a=null},e},render_template=function(e,t){var r=$u.template(e,t);return r="function"==typeof r?$u.template(e)(t):r},convertObjToUrlParams=function(t){for(var e=Object.keys(t).map(function(e){if(_is_string(e))return e+"="+encodeURI(t[e])}),r=[],n=0;n<e.length;++n)_is_string(e[n])&&r.push(e[n]);return 1===r.length?r[0]:r.join("&")},updateUrl=function(){var e=window.location.origin,t=window.location.pathname,r=$.getQueryParameters(),n=window.location.hash,a=getSearchTerm();0<a.length?r.rtd_search=a:delete r.rtd_search;n=t+"?"+(convertObjToUrlParams(r)+n);"http"===e.substring(0,4)&&(n=e+n),window.history.pushState({},null,n)},updateSearchBar=function(){getInputField().value=getSearchTerm()},isModalVisible=function(){var e=document.querySelector(".search__outer__wrapper");return null!==e&&null!==e.style&&null!==e.style.display&&"block"===e.style.display},createDomNode=function(e,t){var r,n=document.createElement(e);for(r in t)n.setAttribute(r,t[r]);return n},_is_string=function(e){return"string"==typeof e&&0<e.length},_is_array=function(e){return!!(Array.isArray(e)&&0<e.length)},get_section_html=function(e,t,r){var n=e.title,a=e.highlights;a.title.length&&(n=a.title[0]);var o=[e.content.substring(0,MAX_SUBSTRING_LIMIT)+" ..."];if(a.content.length)for(var s=a.content,o=[],i=0;i<s.length&&i<MAX_SECTION_RESULTS;++i)o.push("... "+s[i]+" ...");e="".concat(t,"#").concat(e.id);return render_template('<a href="<%= section_link %>">             <div class="outer_div_page_results" id="<%= section_id %>">                 <span class="search__result__subheading">                     <%= section_subheading %>                 </span>                 <% for (var i = 0; i < section_content.length; ++i) { %>                     <p class="search__result__content">                         <%= section_content[i] %>                     </p>                 <% } %>            </div>         </a>         <br class="br-for-hits">',{section_link:e,section_id:"hit__"+r,section_subheading:n,section_content:o})},get_domain_html=function(e,t,r){var n="".concat(t,"#").concat(e.id),a=e.role,o=e.name,t=e.content.substr(0,MAX_SUBSTRING_LIMIT)+" ...",e=e.highlights;return e.name.length&&(o=e.name[0]),e.content.length&&(t=e.content[0]),render_template('<a href="<%= domain_link %>">             <div class="outer_div_page_results" id="<%= domain_id %>">                 <span class="search__result__subheading">                     <%= domain_subheading %>                     <div class="search__domain_role_name">                         <%= domain_role_name %>                     </div>                 </span>                 <p class="search__result__content"><%= domain_content %></p>             </div>         </a>         <br class="br-for-hits">',{domain_link:n,domain_id:"hit__"+r,domain_content:t,domain_subheading:o,domain_role_name:a="["+a+"]"})},generateSingleResult=function(e,t,r){var n=createDomNode("div"),a=e.path,o=e.title,s=e.highlights;s.title.length&&(o=s.title[0]),t!==e.project&&(o+=" "+render_template('<small class="rtd_ui_search_subtitle">                     (from project <%= project %>)                 </small>',{project:e.project})),o+="<br>",n.innerHTML+=render_template('<a href="<%= page_link %>">             <h2 class="search__result__title">                 <%= page_title %>             </h2>         </a>',{page_link:a,page_title:o});for(var i=0;i<e.blocks.length;++i){var l=e.blocks[i],c="";r+=1,"section"===l.type?c=get_section_html(l,a,r):"domain"===l.type&&(c=get_domain_html(l,a,r)),n.innerHTML+=c}return n},generateSuggestionsList=function(e,t){for(var r=createDomNode("div",{class:"search__result__box"}),n=Math.min(MAX_SUGGESTIONS,e.results.length),a=0,o=0;o<n;++o){var s=createDomNode("div",{class:"search__result__single"}),i=generateSingleResult(e.results[o],t,a);s.appendChild(i),r.appendChild(s),a+=e.results[o].blocks.length}return r},removeAllActive=function(){for(var t=document.querySelectorAll(".outer_div_page_results.active"),e=Object.keys(t).map(function(e){return t[e]}),r=1;r<=e.length;++r)e[r-1].classList.remove("active")},addActive=function(e){e=document.querySelector("#hit__"+e);null!==e&&(e.classList.add("active"),e.scrollIntoView({behavior:"smooth",block:"nearest",inline:"start"}))},selectNextResult=function(e){var t=document.querySelectorAll(".outer_div_page_results"),r=document.querySelector(".outer_div_page_results.active"),n=1,a=1;0<t.length&&(null===(t=t[t.length-1]).id||null!==(t=t.id.match(/\d+/))&&(a=Number(t[0]))),null===r||null===r.id||null!==(r=r.id.match(/\d+/))&&(n=Number(r[0]),n+=e?1:-1),n<=0?n=a:a<n&&(n=1),removeAllActive(),addActive(n)},getInputField=function(){var t;try{if(null==(t=document.querySelector("[role='search'] input")))throw"'[role='search'] input' not found"}catch(e){t=document.querySelector("input[name='q']")}return t},getSearchTerm=function(){var e=document.querySelector(".search__outer__input");return null!==e&&e.value||""},removeResults=function(){for(var e=document.querySelectorAll(".search__result__box"),t=0;t<e.length;++t)e[t].parentElement.removeChild(e[t])},getErrorDiv=function(e){var t=createDomNode("div",{class:"search__result__box search__error__box"});return t.innerHTML=e,t},fetchAndGenerateResults=function(e,n){var a=document.querySelector(".search__outer");removeResults();var t=createDomNode("div",{class:"search__result__box"});return t.innerHTML="<strong>Searching ....</strong>",a.appendChild(t),debounce(function(){updateUrl(),updateSearchBar(),$.ajax({url:e,crossDomain:!0,xhrFields:{withCredentials:!0},complete:function(e,t){var r;"success"!==t&&void 0===e.responseJSON||(0<e.responseJSON.results.length?(r=generateSuggestionsList(e.responseJSON,n),removeResults(),a.appendChild(r),a.addEventListener("mouseenter",function(e){removeAllActive()})):(removeResults(),r=getErrorDiv("No results found"),a.appendChild(r)))},error:function(e,t,r){removeResults();var n=getErrorDiv("There was an error. Please try again.");a.appendChild(n)}})},FETCH_RESULTS_DELAY)},generateAndReturnInitialHtml=function(){var e=createDomNode("div",{class:"search__outer__wrapper search__backdrop"});return e.innerHTML='<div class="search__outer">             <div class="search__cross" title="Close">                 \x3c!--?xml version="1.0" encoding="UTF-8"?--\x3e                 <svg class="search__cross__img" width="15px" height="15px" enable-background="new 0 0 612 612" version="1.1" viewBox="0 0 612 612" xml:space="preserve" xmlns="http://www.w3.org/2000/svg">                     <polygon points="612 36.004 576.52 0.603 306 270.61 35.478 0.603 0 36.004 270.52 306.01 0 576 35.478 611.4 306 341.41 576.52 611.4 612 576 341.46 306.01"></polygon>                 </svg>             </div>             <input class="search__outer__input" placeholder="Search ...">             <span class="bar"></span>         </div>         <div class="rtd__search__credits">             Search by <a href="https://readthedocs.org/">Read the Docs</a> & <a href="https://readthedocs-sphinx-search.readthedocs.io/en/latest/">readthedocs-sphinx-search</a>         </div>',e},showSearchModal=function(r){removeResults(),$(".search__outer__wrapper").fadeIn(ANIMATION_TIME,function(){var e=getInputField();e.blur();var t=document.querySelector(".search__outer__input");null!==t&&(void 0!==r&&_is_string(r)?(t.value=r,e.value=r):t.value=e.value,t.focus())})},removeSearchModal=function(){removeResults(),updateSearchBar();var e=document.querySelector(".search__outer__input");null!==e&&(e.value="",e.blur()),updateUrl(),$(".search__outer__wrapper").fadeOut(ANIMATION_TIME)};window.addEventListener("DOMContentLoaded",function(){var n,a,o,s,e,t,i,r,l;window.hasOwnProperty("READTHEDOCS_DATA")?(n=READTHEDOCS_DATA.project,a=READTHEDOCS_DATA.version,o=READTHEDOCS_DATA.language||"en",s=READTHEDOCS_DATA.proxied_api_host||"/_",l=generateAndReturnInitialHtml(),document.body.appendChild(l),e=document.querySelector(".search__outer__wrapper"),t=document.querySelector(".search__outer__input"),l=document.querySelector(".search__cross"),i=null,(r=getInputField()).addEventListener("focus",function(e){showSearchModal()}),t.addEventListener("input",function(e){var t=getSearchTerm(),r=s+"/api/v2/search/?"+convertObjToUrlParams({q:t,project:n,version:a,language:o});0<t.length?(null!==i&&i.cancel(),(i=fetchAndGenerateResults(r,n))()):(debounce(function(){removeResults(),updateUrl()},CLEAR_RESULTS_DELAY)(),updateUrl())}),t.addEventListener("keydown",function(e){var t;40===e.keyCode&&(e.preventDefault(),selectNextResult(!0)),38===e.keyCode&&(e.preventDefault(),selectNextResult(!1)),13===e.keyCode&&(e.preventDefault(),null!==(e=document.querySelector(".outer_div_page_results.active"))?(t=e.parentElement.href,window.location.href=t):(t=getInputField().parentElement,r.value=getSearchTerm(),t.submit()))}),e.addEventListener("click",function(e){e.target.parentNode===document.body&&removeSearchModal()}),l.addEventListener("click",function(e){removeSearchModal()}),document.addEventListener("keydown",function(e){27===e.keyCode&&removeSearchModal()}),document.addEventListener("keydown",function(e){191!==e.keyCode||isModalVisible()||(e.preventDefault(),showSearchModal())}),l=$.getQueryParameters(),_is_array(l.rtd_search)&&(l=decodeURIComponent(l.rtd_search),showSearchModal(l),t.value=l,(l=document.createEvent("Event")).initEvent("input",!0,!0),t.dispatchEvent(l))):console.log("[INFO] Docs are not being served on Read the Docs, readthedocs-sphinx-search will not work.")});