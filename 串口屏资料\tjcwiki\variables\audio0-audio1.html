<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>audio0~audio1-音频通道控制 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="串口HMI颜色代号表" href="hmi_color.html" />
    <link rel="prev" title="pwmf-PWM输出的频率" href="pwmf.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">系统变量</a><ul class="current">
<li class="toctree-l2 current"><a class="reference internal" href="index.html#id2">常用变量</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="sys0-sys2.html">sys0-sys2默认变量</a></li>
<li class="toctree-l3"><a class="reference internal" href="dp.html">dp-当前页面ID</a></li>
<li class="toctree-l3"><a class="reference internal" href="volume.html">volume-系统音量</a></li>
<li class="toctree-l3"><a class="reference internal" href="dims.html">dims-上电默认背光亮度值</a></li>
<li class="toctree-l3"><a class="reference internal" href="dim.html">dim-当前背光亮度值</a></li>
<li class="toctree-l3"><a class="reference internal" href="bauds.html">bauds-上电默认波特率值</a></li>
<li class="toctree-l3"><a class="reference internal" href="baud.html">baud-当前波特率值</a></li>
<li class="toctree-l3"><a class="reference internal" href="ussp.html">ussp-无串口数据自动睡眠时间</a></li>
<li class="toctree-l3"><a class="reference internal" href="thsp.html">thsp-无触摸操作自动睡眠时间</a></li>
<li class="toctree-l3"><a class="reference internal" href="thup.html">thup-睡眠模式下触摸自动唤醒开关</a></li>
<li class="toctree-l3"><a class="reference internal" href="usup.html">usup-睡眠模式下串口数据自动唤醒开关</a></li>
<li class="toctree-l3"><a class="reference internal" href="wup.html">wup-睡眠唤醒后刷新页面设置</a></li>
<li class="toctree-l3"><a class="reference internal" href="sleep.html">sleep-睡眠</a></li>
<li class="toctree-l3"><a class="reference internal" href="lowpower.html">lowpower-睡眠模式设定</a></li>
<li class="toctree-l3"><a class="reference internal" href="bkcmd.html">bkcmd-串口指令执行状态数据返回</a></li>
<li class="toctree-l3"><a class="reference internal" href="delay.html">delay-延时</a></li>
<li class="toctree-l3"><a class="reference internal" href="rand.html">rand-随机数</a></li>
<li class="toctree-l3"><a class="reference internal" href="crcval.html">crcval-crc校验结果</a></li>
<li class="toctree-l3"><a class="reference internal" href="rtc0-rtc6.html">rtc0~rtc6-RTC时钟变量</a></li>
<li class="toctree-l3"><a class="reference internal" href="pio0-pio7.html">pio0~pio7-扩展IO端口</a></li>
<li class="toctree-l3"><a class="reference internal" href="pwm4-pwm7.html">pwm4~pwm7-扩展IO占空比</a></li>
<li class="toctree-l3"><a class="reference internal" href="pwmf.html">pwmf-PWM输出的频率</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">audio0~audio1-音频通道控制</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#audio-1">audio-示例1</a></li>
<li class="toctree-l4"><a class="reference internal" href="#audio-2">audio-示例2</a></li>
<li class="toctree-l4"><a class="reference internal" href="#audio-3">audio-示例3</a></li>
<li class="toctree-l4"><a class="reference internal" href="#audio">audio-相关链接</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="hmi_color.html">串口HMI颜色代号表</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#id3">不常用变量</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">系统变量</a> &raquo;</li>
      <li>audio0~audio1-音频通道控制</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="audio0-audio1">
<h1>audio0~audio1-音频通道控制<a class="headerlink" href="#audio0-audio1" title="此标题的永久链接"></a></h1>
<p>音频通道控制</p>
<p>仅带喇叭的型号（例如X3、X5系列）支持</p>
<p>0-停止;1-播放;2-暂停</p>
<section id="audio-1">
<h2>audio-示例1<a class="headerlink" href="#audio-1" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//暂停0通道的音频播放</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">audio0</span><span class="o">=</span><span class="mi">2</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/audio_1.png" src="../_images/audio_1.png" />
</section>
<section id="audio-2">
<h2>audio-示例2<a class="headerlink" href="#audio-2" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//停止0通道的音频播放</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">audio0</span><span class="o">=</span><span class="mi">0</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/audio_2.png" src="../_images/audio_2.png" />
</section>
<section id="audio-3">
<h2>audio-示例3<a class="headerlink" href="#audio-3" title="此标题的永久链接"></a></h2>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="c1">//继续1通道的音频播放</span>
<span class="linenos">2</span><span class="w"> </span><span class="n">audio1</span><span class="o">=</span><span class="mi">1</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/audio_3.png" src="../_images/audio_3.png" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>仅x5 x3系列支持</p>
<p>play指令用于配置和启动音频播放,系统变量audio0,audio1用于控制通道状态。</p>
<p>只有当通道状态在暂停的时候，才能配置为继续播放。如果通道状态为停止，将不能配置为继续播放，需要使用play指令来配置并启动播放。</p>
<p>音频播放功能是全局的，不属于某个页面，因此play指令启动播放后，即便是跳转页面，音频依然会继续播放，如果希望离开页面后停止播放，可以在页面的离开事件中使用audio0/audio1系统变量来关闭或暂停指定通道的音频播放状态。</p>
</div>
</section>
<section id="audio">
<h2>audio-相关链接<a class="headerlink" href="#audio" title="此标题的永久链接"></a></h2>
<p><a class="reference internal" href="../commands/play.html#play"><span class="std std-ref">play-音频播放</span></a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="pwmf.html" class="btn btn-neutral float-left" title="pwmf-PWM输出的频率" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="hmi_color.html" class="btn btn-neutral float-right" title="串口HMI颜色代号表" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>