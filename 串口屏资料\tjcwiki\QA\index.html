<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>常见问题 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="ESD能过多少V，EMI性能如何" href="QA1.html" />
    <link rel="prev" title="视频教程" href="../video.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1"><a class="reference internal" href="../grammar/index.html">书写语法</a></li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">常见问题</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#id2">硬件相关</a><ul>
<li class="toctree-l3"><a class="reference internal" href="QA1.html">ESD能过多少V，EMI性能如何</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA2.html">串口通讯线最长能接多少米</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA3.html">通讯口电压多少V，是否可以直接接单片机</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA7.html">屏幕通电后不断的闪烁(不断重启)</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA37.html">喇叭相关问题</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA95.html">关于串口工具(cp2102/ft232/ch340/pl2303)</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA45.html">如何修改控件显示的字体</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA47.html">串口驱动相关问题</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA49.html">串口电平状态</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA50.html">关于一上电就关闭屏幕背光</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA55.html">结构兼容性</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA71.html">拓展IO相关问题</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA72.html">如何修改设备型号</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA74.html">屏幕边框为什么不对称</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA79.html">外部供电接法</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA126.html">如何判断屏幕是否支持蜂鸣器</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA127.html">如何判断屏幕是否支持RTC实时时钟</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA128.html">为什么232通讯不能超过256000</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA129.html">如何判断屏幕是否支持喇叭</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA133.html">串口屏常用接口型号说明</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#id3">软件相关</a><ul>
<li class="toctree-l3"><a class="reference internal" href="QA4.html">设计好的工程有哪些下载方式</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA5.html">tft文件串口/SD卡下载失败如何解决</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA6.html">调试或下载时联机失败</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA8.html">指定字库</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA10.html">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA11.html">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA12.html">运行中控件属性被修改后，离开页面再回来，不希望属性回到初始值怎么操作</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA13.html">SD卡有哪些作用</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA14.html">program.s详解</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA15.html">串口屏卡顿的原因</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA16.html">文本控件或按钮控件不显示我输入的字符内容或显示不全</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA17.html">单片机发指令给屏幕没有反应</a></li>
<li class="toctree-l3"><a class="reference internal" href="baudrate.html">如何配置亮度,主动解析,波特率</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA19.html">系统内置的触摸键盘怎么修改风格</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA20.html">滑块或进度条控件默认是横向，为什么我设为竖向后没有变化，还是显示横向呢</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA24.html">单片机和串口屏通信为什么单片机上电后要延时一会再发指令，延时多久合适</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA25.html">单片机上电后为什么要先发一次 0x00 0xff 0xff 0xff给屏幕</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA26.html">屏幕地址怎么设置，怎么使用</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA27.html">注释乱码或者不显示</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA28.html">如何更改控件的前后图层关系</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA31.html">如何设计多语言界面</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA34.html">K系列和T系列如何实现透明效果</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA35.html">单片机如何接收和解析串口屏数据</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA36.html">如何对掉电存储空间进行初始化</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA41.html">转义字符相关</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA42.html">使用sscom时，sscom可以接收到串口数据，但是sscom发送的数据屏幕接收不到</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA54.html">如何实现按位取反</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA62.html">如何实现按位同或运算</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA81.html">页面和页面控件的区别</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA88.html">休眠替代方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA91.html">淘晶驰串口屏支持哪些通讯格式</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA98.html">dim和dims的区别</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA99.html">baud和bauds的区别</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA125.html">能不能用printf一次性发送多条指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA134.html">如何显示和计算百分比</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA135.html">内存文件存储区相关</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA136.html">页面名称乱码</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#id4">错误提示</a><ul>
<li class="toctree-l3"><a class="reference internal" href="QA9.html">编译报错：XXX初始值无效</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA18.html">单片机发指令给屏幕，屏幕返回1A FF FF FF或1C FF FF FF四个字节的HEX数据</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA38.html">串口连接不上屏幕</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA39.html">串口屏黑屏原因</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA40.html">变量名称无效zstr</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA43.html">file version is too low</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA44.html">报错:file is too large for destination device</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA29.html">在做字库的时候有部分字体无法选择</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA30.html">如何解决调用系统键盘给控件赋值无效</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA33.html">软件输出生产文件，出现提示框拒绝访问</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA32.html">window7无法打开软件安装包</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA56.html">用串口下载程序，成功但显示Update failed check error</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA60.html">串口屏开机时死机/不断的闪烁/不断重启</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA63.html">调试时可以正常显示，但是下载进屏幕就无法使用</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA68.html">视频IDO文件方向与当前工程显示方向不一致</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA70.html">提示语句错误：无前括号</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA75.html">提示转义字符使用错误</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA83.html">编译时提示变量名称无效</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA84.html">编译时提示无效指令</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA89.html">提示Show Picture Error</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA90.html">下载一半失败后下载不进去</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA102.html">输入中文时上位机卡死</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA104.html">no find cpu model</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA108.html">提示：请先选择控件</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA109.html">提示：没有控件可以粘贴</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA111.html">明明型号是对的，但是烧录的时候提示Model does not match</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA112.html">MAC电脑下拷贝文件时提示有多个TFT文件</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA113.html">错误的资源文件或者资源文件已经受损</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA123.html">烧录文件提示 open file error</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#id5">其他</a><ul>
<li class="toctree-l3"><a class="reference internal" href="QA46.html">如何查看编译时输出的信息</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA48.html">不同页面之间的同名控件有什么联系</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA51.html">在定时器0里面加入使能定时器1的语句为什么系统不执行</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA52.html">大小端模式</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA53.html">如何删除控件</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA57.html">使用串口下载工程速度慢怎么办</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA58.html">printf()重定向之后，发送命令和结束符</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA59.html">实现自己的printf函数</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA61.html">单片机如何控制屏幕</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA64.html">虚拟sd卡文件夹如何打开</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA65.html">如何实现页面滑动切换</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA66.html">如何设置页面背景</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA67.html">HMI文件和TFT文件的关系</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA69.html">界面旋转/翻转/方向</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA73.html">串口屏如何制作弹窗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA76.html">utf8字库下如何只选择汉字</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA77.html">TFT文件如何下载到串口屏中</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA78.html">只有屏/tft文件,或者源HMI工程丢了,能反编译出HMI文件吗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA80.html">如何导入导出page文件</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA82.html">屏幕保护制作</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA85.html">二维码控件是gb2312还是utf8编码</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA86.html">修改工程编码为utf8或者GB2312</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA87.html">文本数据显示不完全</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA92.html">键盘无法输入中文</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA93.html">单片机如何判断串口屏有没有接入</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA94.html">实现按键锁定功能</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA96.html">串口屏的串口缓冲区大小</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA97.html">主动解析下如何提取负数</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA100.html">带外壳产品安装示意图</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA101.html">显示比例不对，正方形变成长方形，圆型变成椭圆</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA103.html">发送中文汉字相关问题</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA105.html">为什么关闭小板上的电源时，RX灯会亮起</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA106.html">变量控件名和页面名称可以一样吗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA107.html">如何使用TFT文件下载助手(TFTFileDownload)下载工程</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA110.html">单片机3.3V的串口可以直接接串口屏的串口吗</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA114.html">如何安装新字体</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA115.html">如何导入字库</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA116.html">如何导入图片</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA117.html">如何导入动画</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA118.html">如何导入视频</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA119.html">如何导入音频</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA120.html">串口屏连接单片机开发板注意事项</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA121.html">字符编码相关详解——串口怎么发送字符和汉字</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA122.html">为什么转换后的图片/视频/音频体积变大了</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA124.html">如何测量屏幕的尺寸大小</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA130.html">MAC电脑如何开发淘晶驰串口屏</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA131.html">数据记录文件结构分析</a></li>
<li class="toctree-l3"><a class="reference internal" href="QA132.html">哪些指令和操作会消耗flash寿命</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
      <li>常见问题</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>常见问题<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<section id="id2">
<h2>硬件相关<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="QA1.html">ESD能过多少V，EMI性能如何</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA2.html">串口通讯线最长能接多少米</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA3.html">通讯口电压多少V，是否可以直接接单片机</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA7.html">屏幕通电后不断的闪烁(不断重启)</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA37.html">喇叭相关问题</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA95.html">关于串口工具(cp2102/ft232/ch340/pl2303)</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA45.html">如何修改控件显示的字体</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA47.html">串口驱动相关问题</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA49.html">串口电平状态</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA50.html">关于一上电就关闭屏幕背光</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA55.html">结构兼容性</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA71.html">拓展IO相关问题</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA72.html">如何修改设备型号</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA74.html">屏幕边框为什么不对称</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA79.html">外部供电接法</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA126.html">如何判断屏幕是否支持蜂鸣器</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA127.html">如何判断屏幕是否支持RTC实时时钟</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA128.html">为什么232通讯不能超过256000</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA129.html">如何判断屏幕是否支持喇叭</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA133.html">串口屏常用接口型号说明</a></li>
</ul>
</div>
</section>
<section id="id3">
<h2>软件相关<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="QA4.html">设计好的工程有哪些下载方式</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA5.html">tft文件串口/SD卡下载失败如何解决</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA6.html">调试或下载时联机失败</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA8.html">指定字库</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA10.html">哪些控件属性可以运行中修改，哪些不能运行中修改，绿色属性和黑色属性有什么区别？</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA11.html">txt属性和val属性有什么区别，为什么有些属性赋值要加双引号，有些不用加双引号</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA12.html">运行中控件属性被修改后，离开页面再回来，不希望属性回到初始值怎么操作</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA13.html">SD卡有哪些作用</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA14.html">program.s详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA15.html">串口屏卡顿的原因</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA16.html">文本控件或按钮控件不显示我输入的字符内容或显示不全</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA17.html">单片机发指令给屏幕没有反应</a></li>
<li class="toctree-l1"><a class="reference internal" href="baudrate.html">如何配置亮度,主动解析,波特率</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA19.html">系统内置的触摸键盘怎么修改风格</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA20.html">滑块或进度条控件默认是横向，为什么我设为竖向后没有变化，还是显示横向呢</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA24.html">单片机和串口屏通信为什么单片机上电后要延时一会再发指令，延时多久合适</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA25.html">单片机上电后为什么要先发一次 0x00 0xff 0xff 0xff给屏幕</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA26.html">屏幕地址怎么设置，怎么使用</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA27.html">注释乱码或者不显示</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA28.html">如何更改控件的前后图层关系</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA31.html">如何设计多语言界面</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA34.html">K系列和T系列如何实现透明效果</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA35.html">单片机如何接收和解析串口屏数据</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA36.html">如何对掉电存储空间进行初始化</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA41.html">转义字符相关</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA42.html">使用sscom时，sscom可以接收到串口数据，但是sscom发送的数据屏幕接收不到</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA54.html">如何实现按位取反</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA62.html">如何实现按位同或运算</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA81.html">页面和页面控件的区别</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA88.html">休眠替代方法</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA91.html">淘晶驰串口屏支持哪些通讯格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA98.html">dim和dims的区别</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA99.html">baud和bauds的区别</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA125.html">能不能用printf一次性发送多条指令</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA134.html">如何显示和计算百分比</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA135.html">内存文件存储区相关</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA136.html">页面名称乱码</a></li>
</ul>
</div>
</section>
<section id="id4">
<h2>错误提示<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="QA9.html">编译报错：XXX初始值无效</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA18.html">单片机发指令给屏幕，屏幕返回1A FF FF FF或1C FF FF FF四个字节的HEX数据</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA38.html">串口连接不上屏幕</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA39.html">串口屏黑屏原因</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA40.html">变量名称无效zstr</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA43.html">file version is too low</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA44.html">报错:file is too large for destination device</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA29.html">在做字库的时候有部分字体无法选择</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA30.html">如何解决调用系统键盘给控件赋值无效</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA33.html">软件输出生产文件，出现提示框拒绝访问</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA32.html">window7无法打开软件安装包</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA56.html">用串口下载程序，成功但显示Update failed check error</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA60.html">串口屏开机时死机/不断的闪烁/不断重启</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA63.html">调试时可以正常显示，但是下载进屏幕就无法使用</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA68.html">视频IDO文件方向与当前工程显示方向不一致</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA70.html">提示语句错误：无前括号</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA75.html">提示转义字符使用错误</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA83.html">编译时提示变量名称无效</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA84.html">编译时提示无效指令</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA89.html">提示Show Picture Error</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA90.html">下载一半失败后下载不进去</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA102.html">输入中文时上位机卡死</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA104.html">no find cpu model</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA108.html">提示：请先选择控件</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA109.html">提示：没有控件可以粘贴</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA111.html">明明型号是对的，但是烧录的时候提示Model does not match</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA112.html">MAC电脑下拷贝文件时提示有多个TFT文件</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA113.html">错误的资源文件或者资源文件已经受损</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA123.html">烧录文件提示 open file error</a></li>
</ul>
</div>
</section>
<section id="id5">
<h2>其他<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="QA46.html">如何查看编译时输出的信息</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA48.html">不同页面之间的同名控件有什么联系</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA51.html">在定时器0里面加入使能定时器1的语句为什么系统不执行</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA52.html">大小端模式</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA53.html">如何删除控件</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA57.html">使用串口下载工程速度慢怎么办</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA58.html">printf()重定向之后，发送命令和结束符</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA59.html">实现自己的printf函数</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA61.html">单片机如何控制屏幕</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA64.html">虚拟sd卡文件夹如何打开</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA65.html">如何实现页面滑动切换</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA66.html">如何设置页面背景</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA67.html">HMI文件和TFT文件的关系</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA69.html">界面旋转/翻转/方向</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA73.html">串口屏如何制作弹窗</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA76.html">utf8字库下如何只选择汉字</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA77.html">TFT文件如何下载到串口屏中</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA78.html">只有屏/tft文件,或者源HMI工程丢了,能反编译出HMI文件吗</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA80.html">如何导入导出page文件</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA82.html">屏幕保护制作</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA85.html">二维码控件是gb2312还是utf8编码</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA86.html">修改工程编码为utf8或者GB2312</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA87.html">文本数据显示不完全</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA92.html">键盘无法输入中文</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA93.html">单片机如何判断串口屏有没有接入</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA94.html">实现按键锁定功能</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA96.html">串口屏的串口缓冲区大小</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA97.html">主动解析下如何提取负数</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA100.html">带外壳产品安装示意图</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA101.html">显示比例不对，正方形变成长方形，圆型变成椭圆</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA103.html">发送中文汉字相关问题</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA105.html">为什么关闭小板上的电源时，RX灯会亮起</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA106.html">变量控件名和页面名称可以一样吗</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA107.html">如何使用TFT文件下载助手(TFTFileDownload)下载工程</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA110.html">单片机3.3V的串口可以直接接串口屏的串口吗</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA114.html">如何安装新字体</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA115.html">如何导入字库</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA116.html">如何导入图片</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA117.html">如何导入动画</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA118.html">如何导入视频</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA119.html">如何导入音频</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA120.html">串口屏连接单片机开发板注意事项</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA121.html">字符编码相关详解——串口怎么发送字符和汉字</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA122.html">为什么转换后的图片/视频/音频体积变大了</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA124.html">如何测量屏幕的尺寸大小</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA130.html">MAC电脑如何开发淘晶驰串口屏</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA131.html">数据记录文件结构分析</a></li>
<li class="toctree-l1"><a class="reference internal" href="QA132.html">哪些指令和操作会消耗flash寿命</a></li>
</ul>
</div>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../video.html" class="btn btn-neutral float-left" title="视频教程" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="QA1.html" class="btn btn-neutral float-right" title="ESD能过多少V，EMI性能如何" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>