# Claude Code 永久配置说明

## 配置完成状态
✅ **环境变量已成功设置到用户级别**

### 已配置的环境变量：
- `ANTHROPIC_BASE_URL`: https://pmpjfbhq.cn-nb1.rainapp.top
- `ANTHROPIC_API_KEY`: sk-BxgVjVtoWWq7D23TmTBKPDJqxkK8IsHmUau3mtnCviPg1yHC
- `ANTHROPIC_AUTH_TOKEN`: sk-BxgVjVtoWWq7D23TmTBKPDJqxkK8IsHmUau3mtnCviPg1yHC

## 使用方法

### 1. 重启应用程序
为了让新的环境变量生效，请：
- 重启您的终端（PowerShell/CMD）
- 重启您的IDE（如VS Code、PyCharm等）
- 重启Claude Code应用程序

### 2. 验证配置
运行以下文件来验证配置是否正确：
```powershell
.\verify_claude_env_en.ps1
```

### 3. 手动设置（如果需要）
如果自动设置失败，可以手动运行：
```batch
.\set_claude_env.bat
```

## 配置文件说明

### set_claude_env.bat
- 用于设置环境变量的批处理文件
- 使用 `setx` 命令将变量永久保存到用户配置文件

### verify_claude_env_en.ps1
- 用于验证环境变量设置的PowerShell脚本
- 显示用户级和当前进程级的环境变量状态

## 故障排除

### 如果Claude Code仍然无法连接：
1. 确认已重启相关应用程序
2. 检查网络连接
3. 验证API密钥是否有效
4. 确认代理服务器地址是否正确

### 如果需要修改配置：
1. 编辑 `set_claude_env.bat` 文件
2. 重新运行该文件
3. 重启应用程序

## 注意事项
- 环境变量设置为用户级别，只对当前用户有效
- 修改后需要重启应用程序才能生效
- 请妥善保管API密钥，不要泄露给他人
