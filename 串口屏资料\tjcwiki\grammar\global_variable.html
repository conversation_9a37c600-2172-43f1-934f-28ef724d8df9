<!DOCTYPE html>
<html class="writer-html5" lang="zh-CN" >
<head>
  <meta charset="utf-8" /><meta name="generator" content="Docutils 0.17.1: http://docutils.sourceforge.net/" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>跨页面赋值，全局变量操作 &mdash; 淘晶驰串口屏资料中心 1.1.0-2025-07-24 10:19:55 文档</title>
      <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css/theme.css" type="text/css" />
      <link rel="stylesheet" href="../_static/css\rtd_sphinx_search.min.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
        <script src="../_static/jquery.js"></script>
        <script src="../_static/underscore.js"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
        <script src="../_static/doctools.js"></script>
        <script src="../_static/translations.js"></script>
        <script src="../_static/js\rtd_sphinx_search.min.js"></script>
        <script src="../_static/custom.js"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="索引" href="../genindex.html" />
    <link rel="search" title="搜索" href="../search.html" />
    <link rel="next" title="HMI逻辑语句" href="hmi_logic.html" />
    <link rel="prev" title="运算操作" href="arithmetic_operation.html" />
    <link href="../_static/style.css" rel="stylesheet" type="text/css">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">

</head>

<body class="wy-body-for-nav">
  <!-- <style>
      a:link {color: #3091d1}
      a:visited {color: #3091d1}
      a:hover {color: #3091d1}
      a:active {color: #3091d1}
  </style> --> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../index.html" class="icon icon-home"> 淘晶驰串口屏资料中心
            <img src="../_static/tjc.png" class="logo" alt="Logo"/>
          </a>
              <div class="version">
                1.1.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="在文档中搜索" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">淘晶驰教程</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../start/index.html">快速入门</a></li>
<li class="toctree-l1"><a class="reference internal" href="../debug/index.html">串口屏调试</a></li>
<li class="toctree-l1"><a class="reference internal" href="../widgets/index.html">控件详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../commands/index.html">基本指令集</a></li>
<li class="toctree-l1"><a class="reference internal" href="../variables/index.html">系统变量</a></li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">书写语法</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="assignment_operation.html">赋值操作</a></li>
<li class="toctree-l2"><a class="reference internal" href="arithmetic_operation.html">运算操作</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">跨页面赋值，全局变量操作</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id2">全局变量-常规使用</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id3">全局变量-特殊控件</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id4">不同页面的相同名称的控件是什么关系</a></li>
<li class="toctree-l3"><a class="reference internal" href="#c">全局变量-c语言示例</a></li>
<li class="toctree-l3"><a class="reference internal" href="#sscom">全局变量-sscom串口工具发送示例</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id5">全局变量-样例工程下载</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="hmi_logic.html">HMI逻辑语句</a></li>
<li class="toctree-l2"><a class="reference internal" href="name_array.html">数组/名称组使用说明</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../return/index.html">串口屏返回数据格式</a></li>
<li class="toctree-l1"><a class="reference internal" href="../advanced/index.html">串口屏高级应用详解</a></li>
<li class="toctree-l1"><a class="reference internal" href="../download/index.html">资料下载</a></li>
<li class="toctree-l1"><a class="reference internal" href="../video.html">视频教程</a></li>
<li class="toctree-l1"><a class="reference internal" href="../QA/index.html">常见问题</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">产品选型和规格书</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../product/index.html">产品选型和规格书</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">电赛专题</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../nuedc/index.html">入门</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">淘晶驰串口屏资料中心</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home"></a> &raquo;</li>
          <li><a href="index.html">书写语法</a> &raquo;</li>
      <li>跨页面赋值，全局变量操作</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="id1">
<h1>跨页面赋值，全局变量操作<a class="headerlink" href="#id1" title="此标题的永久链接"></a></h1>
<section id="id2">
<h2>全局变量-常规使用<a class="headerlink" href="#id2" title="此标题的永久链接"></a></h2>
<p>多数情况下，我们都是在操作当面页面的控件属性，如果需要操作其他页面的控件属性需要提前将对应的控件设置为全局</p>
<img alt="../_images/global_variable_1.jpg" src="../_images/global_variable_1.jpg" />
<p>修改了vscope属性后，控件的名称会由黄色变为黑色</p>
<img alt="../_images/global_variable_3.jpg" src="../_images/global_variable_3.jpg" />
<p>请按如下书写方式:</p>
<p>[页面].[控件].[属性]=XXX</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>//给page0页面的n0.val属性赋值123
<span class="linenos"> 2</span>page0.n0.val<span class="o">=</span><span class="m">123</span>
<span class="linenos"> 3</span>
<span class="linenos"> 4</span>//给page1页面的n0.val属性赋值456
<span class="linenos"> 5</span>page1.n0.val<span class="o">=</span><span class="m">456</span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span>//给main页面的t0.txt属性赋值<span class="s2">&quot;abc&quot;</span>
<span class="linenos"> 8</span>main.t0.txt<span class="o">=</span><span class="s2">&quot;abc&quot;</span>
<span class="linenos"> 9</span>
<span class="linenos">10</span>//给main页面的t0.txt属性赋值<span class="s2">&quot;123&quot;</span>
<span class="linenos">11</span>main.t0.txt<span class="o">=</span><span class="s2">&quot;123&quot;</span>
<span class="linenos">12</span>
<span class="linenos">13</span>//把set页面的t3.txt赋值给main页面的t0.txt
<span class="linenos">14</span>main.t0.txt<span class="o">=</span>set.t3.txt
<span class="linenos">15</span>
<span class="linenos">16</span>//给set页面的t4.txt赋值<span class="s2">&quot;abc&quot;</span>
<span class="linenos">17</span>set.t4.txt<span class="o">=</span><span class="s2">&quot;abc&quot;</span>
<span class="linenos">18</span>
<span class="linenos">19</span>//将set的页面t3.txt、t4.txt、t5.txt的文本内容拼接后赋值给main页面的t0.txt
<span class="linenos">20</span>main.t0.txt<span class="o">=</span>set.t3.txt+set.t4.txt+set.t5.txt
<span class="linenos">21</span>
<span class="linenos">22</span>//将set的页面t3.txt、拼接字符串<span class="s2">&quot;abc&quot;</span>后赋值给main页面的t0.txt
<span class="linenos">23</span>main.t0.txt<span class="o">=</span>set.t3.txt+<span class="s2">&quot;abc&quot;</span>
</pre></div>
</div>
<img alt="../_images/global_variable_4.jpg" src="../_images/global_variable_4.jpg" />
<p>如果不同页面有名称相同的控件需要赋值不同的数据，可以将他们设置为全局</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>//给main页面的t0.txt属性赋值<span class="s2">&quot;123&quot;</span>
<span class="linenos"> 2</span>main.t0.txt<span class="o">=</span><span class="s2">&quot;123&quot;</span>
<span class="linenos"> 3</span>
<span class="linenos"> 4</span>//给set页面的t0.txt属性赋值<span class="s2">&quot;abc&quot;</span>
<span class="linenos"> 5</span>set.t0.txt<span class="o">=</span><span class="s2">&quot;abc&quot;</span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span>//给about页面的t0.txt属性赋值<span class="s2">&quot;哈哈哈&quot;</span>
<span class="linenos"> 8</span>about.t0.txt<span class="o">=</span><span class="s2">&quot;哈哈哈&quot;</span>
<span class="linenos"> 9</span>
<span class="linenos">10</span>//也可以使用covx来转换txt和val
<span class="linenos">11</span>covx main.n0.val,set.t0.txt,0,0
</pre></div>
</div>
<img alt="../_images/global_variable_5.jpg" src="../_images/global_variable_5.jpg" />
<p>如果需要跨页面修改页面的背景颜色，需要将页面控件设置为全局</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span>//第一个main是页面名称，第二个main是页面控件，页面控件与页面名称同名
<span class="linenos">2</span>main.main.bco<span class="o">=</span>BLACK
</pre></div>
</div>
<img alt="../_images/global_variable_6.jpg" src="../_images/global_variable_6.jpg" />
<div class="admonition attention">
<p class="admonition-title">注意</p>
<p>1.跨页面操作控件属性的时候，不管是读取还是赋值，被操作控件的vscope属性必须设置为全局（默认是私有），否则操作会失败。</p>
<p>2.click vis tsw指令无法跨页面触发控件、定时器控件只能在当前页面运行、曲线波形控件只能在当前页面添加数据点。</p>
</div>
<p>在program.s中定义的变量也是全局变量，且只能是数字类型的变量，可以在任意页面进行使用</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="w"> </span><span class="n">sys0</span><span class="o">=</span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="w"></span>
<span class="linenos">2</span>
<span class="linenos">3</span><span class="w"> </span><span class="n">n0</span><span class="p">.</span><span class="n">val</span><span class="o">=</span><span class="n">sys1</span><span class="w"></span>
</pre></div>
</div>
<img alt="../_images/global_variable_7.jpg" src="../_images/global_variable_7.jpg" />
</section>
<section id="id3">
<h2>全局变量-特殊控件<a class="headerlink" href="#id3" title="此标题的永久链接"></a></h2>
<p>1、定时器控件</p>
<p>定时器控件虽然可以设置为全局，但是定时器无法跨页面运行，定时器只能在定时器所在的页面运行，一旦切换页面，定时器就会停止运行</p>
<p>2、触摸捕捉控件</p>
<p>触摸捕捉控件设置为全局是无意义的</p>
<p>3、数据记录控件</p>
<p>数据记录控件只能是全局的</p>
<p>4、文件流控件</p>
<p>文件流控件只能是私有的</p>
</section>
<section id="id4">
<h2>不同页面的相同名称的控件是什么关系<a class="headerlink" href="#id4" title="此标题的永久链接"></a></h2>
<p>比如page0页面的n0控件和page1页面的n0控件有什么关系</p>
<p>这是两个独立的控件</p>
<p>打个比方，某户人姓张，隔壁老王姓王，老张的儿子叫小明，老王的儿子也叫小明</p>
<p>在老张家时，别人只喊“小明”两个字，那就是在喊“张.小明”</p>
<p>在老王家时，别人只喊“小明”两个字，那就是在喊“王.小明”</p>
<p>在老张家想喊老王家的小明，那就得加上具体的姓氏，就是“王.小明”</p>
<p>同理，page0.n0就是在说明是page0页面的n0，page1.n0就是在说明是page1页面的n0</p>
</section>
<section id="c">
<h2>全局变量-c语言示例<a class="headerlink" href="#c" title="此标题的永久链接"></a></h2>
<p>单片机通过串口给全局变量赋值</p>
<p>给文本控件赋值时如果显示的文本不全，请检查控件的txt_maxl属性</p>
<div class="highlight-c notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;main.t0.txt=</span><span class="se">\&quot;</span><span class="s">abc</span><span class="se">\&quot;\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w">          </span><span class="c1">//给main页面的t0文本控件赋值</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;page0.t2.txt=</span><span class="se">\&quot;</span><span class="s">%d</span><span class="se">\&quot;\xff\xff\xff</span><span class="s">&quot;</span><span class="p">,</span><span class="n">a</span><span class="p">);</span><span class="w">        </span><span class="c1">//给page0页面的t2文本控件赋值</span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;set.t3.txt=</span><span class="se">\&quot;</span><span class="s">abc</span><span class="se">\r\n</span><span class="s">123</span><span class="se">\&quot;\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w">    </span><span class="c1">//给set页面的t3文本控件赋值</span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;set.n0.val=12345</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w">             </span><span class="c1">//给set页面的n0数字控件赋值</span>
<span class="linenos"> 8</span>
<span class="linenos"> 9</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;sys0=1</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w">                       </span><span class="c1">//给全局变量sys0赋值，sys0定义在program.s中</span>
<span class="linenos">10</span>
<span class="linenos">11</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;dim=100</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w">                      </span><span class="c1">//设置亮度，dim是系统变量</span>
<span class="linenos">12</span>
<span class="linenos">13</span><span class="w"> </span><span class="n">printf</span><span class="p">(</span><span class="s">&quot;volume=100</span><span class="se">\xff\xff\xff</span><span class="s">&quot;</span><span class="p">);</span><span class="w">                   </span><span class="c1">//设置音量，volume是系统变量</span>
</pre></div>
</div>
</section>
<section id="sscom">
<h2>全局变量-sscom串口工具发送示例<a class="headerlink" href="#sscom" title="此标题的永久链接"></a></h2>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="n">main</span><span class="o">.</span><span class="n">t0</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;abc&quot;</span>\<span class="n">xff</span>\<span class="n">xff</span>\<span class="n">xff</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="n">page0</span><span class="o">.</span><span class="n">t2</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;30&quot;</span>\<span class="n">xff</span>\<span class="n">xff</span>\<span class="n">xff</span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="nb">set</span><span class="o">.</span><span class="n">t3</span><span class="o">.</span><span class="n">txt</span><span class="o">=</span><span class="s2">&quot;abc</span><span class="se">\\</span><span class="s2">r123&quot;</span>\<span class="n">xff</span>\<span class="n">xff</span>\<span class="n">xff</span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span><span class="nb">set</span><span class="o">.</span><span class="n">n0</span><span class="o">.</span><span class="n">val</span><span class="o">=</span><span class="mi">12345</span>\<span class="n">xff</span>\<span class="n">xff</span>\<span class="n">xff</span>
<span class="linenos"> 8</span>
<span class="linenos"> 9</span><span class="n">sys0</span><span class="o">=</span><span class="mi">1</span>\<span class="n">xff</span>\<span class="n">xff</span>\<span class="n">xff</span>
<span class="linenos">10</span>
<span class="linenos">11</span><span class="n">dim</span><span class="o">=</span><span class="mi">100</span>\<span class="n">xff</span>\<span class="n">xff</span>\<span class="n">xff</span>
<span class="linenos">12</span>
<span class="linenos">13</span><span class="n">volume</span><span class="o">=</span><span class="mi">100</span>\<span class="n">xff</span>\<span class="n">xff</span>\<span class="n">xff</span>
</pre></div>
</div>
<img alt="../_images/global_variable_2.jpg" src="../_images/global_variable_2.jpg" />
</section>
<section id="id5">
<h2>全局变量-样例工程下载<a class="headerlink" href="#id5" title="此标题的永久链接"></a></h2>
<p>演示工程下载链接：</p>
<p><a class="reference external" href="http://filedown.tjc1688.com/tjcwiki/tjcwiki_resource_collection/书写语法/全局变量.HMI">《全局变量》演示工程下载</a></p>
</section>
</section>


           </div>
          </div>
          


<footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="arithmetic_operation.html" class="btn btn-neutral float-left" title="运算操作" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> 上一页</a>
        <a href="hmi_logic.html" class="btn btn-neutral float-right" title="HMI逻辑语句" accesskey="n" rel="next">下一页 <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p></p>
  </div>

  

<a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13060496号-1</a>
<br>
<!-- <hr /> -->
<a href="https://www.tjc1688.com/" target="_blank"> 
<div id="current_date">
    <script>
        date = new Date();
        year = date.getFullYear();
        document.getElementById("current_date").innerHTML ="&#169;2013 - "+ year + "深圳市淘晶驰电子有限公司 www.tjc1688.com";
    </script>
</div>	
</a>
<!-- <hr /> -->
<!-- <br> -->
<a href="http://wiki.tjc1688.com/download/development_doc.html" target="_blank">开发文档下载</a>
&nbsp
<a href="http://wiki.tjc1688.com/download/usart_hmi.html" target="_blank">上位机下载</a>
&nbsp
<a href="https://space.bilibili.com/2009481000/channel/collectiondetail?sid=106537" target="_blank">视频教程</a>



</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>